import { Locator, Page } from '@playwright/test';
import { PageWithFilters } from '../PageWithFilters';

export class CompanyListPage extends PageWithFilters {
  public readonly vendorCards;
  public readonly uploadButton;
  public readonly firstHeading;
  public readonly tableViewButton;
  public readonly cardViewButton;
  public readonly tableRows;
  public readonly nioPagination;
  public readonly loadMoreButton;
  public readonly nioFilters;
  readonly filtersContainer: Locator;
  readonly filters: {
    search: Locator;
    category: Locator;
    country: Locator;
    technologies: Locator;
    industry: Locator;
  };

  constructor(page: Page) {
    super(page, page.getByRole('list', { name: 'Company list' }).getByRole('listitem'));
    this.vendorCards = this.page.getByTestId('company-card');
    this.uploadButton = this.page.getByRole('link', { name: 'upload' });
    this.firstHeading = this.page.getByRole('heading').first();
    this.tableViewButton = this.page.getByTestId('nio-switch-right');
    this.cardViewButton = this.page.getByTestId('nio-switch-left');
    this.tableRows = this.page.getByTestId('vendor-table-row');
    this.nioPagination = this.page.getByTestId('nio-pagination');
    this.loadMoreButton = this.page.getByTestId('load-more-button');
    this.nioFilters = this.page.getByTestId('nio-filter-field');
    this.filtersContainer = page.getByRole('list', { name: 'Filters' });
    this.filters = {
      search: this.filtersContainer.getByRole('listitem', { name: 'Search' }),
      category: this.filtersContainer.getByRole('listitem', { name: 'Category' }),
      country: this.filtersContainer.getByRole('listitem', { name: 'Country' }),
      technologies: this.filtersContainer.getByRole('listitem', { name: 'Technologies' }),
      industry: this.filtersContainer.getByRole('listitem', { name: 'Industry' }),
    };
  }

  async goto() {
    await this.page.goto('/');
    await this.mainMenuToggleButton.click();
    await this.navigation.getByRole('link', { name: 'Vendor Hub' }).click();
    await this.page.waitForURL('/companies');
  }
}
