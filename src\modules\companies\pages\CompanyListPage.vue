<script setup lang="ts">
import { ref, watch } from 'vue';
import CompanyList from '@/modules/companies/components/CompanyList.vue';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import CompanyListSkeleton from '@/modules/companies/components/skeletons/CompanyListSkeleton.vue';
import CompanyListTableSkeleton from '@/modules/companies/components/skeletons/CompanyListTableSkeleton.vue';
import CompanyFilter from '@/modules/companies/components/CompanyFilter.vue';
import { useVendors } from '@/modules/companies/composables/useVendors';
import Loader from '@/common/components/Loader.vue';

const isList = ref(localStorage.getItem('isList') === 'true');
const {
  companies,
  filters,
  page,
  meta,
  filterComponents,
  lazyLoad,
  isLoadingFirstTime,
  isLoading,
} = useVendors(!isList.value);

watch(isList, newValue => {
  lazyLoad.value = !newValue;
  localStorage.setItem('isList', newValue.toString());
});

</script>

<template>
  <PageContainerWrapper transparent>
    <!-- Vendors Filter -->
    <div class="mb-5">
      <CompanyFilter
        v-model:filters="filters"
        v-model:list-view="isList"
        :filter-components="filterComponents"
      />
    </div>

    <!-- Vendors List -->
    <div class="relative">
      <div v-if="isLoadingFirstTime" class="absolute top-0 left-0 w-full h-full">
        <CompanyListTableSkeleton v-if="isList" />
        <CompanyListSkeleton v-else />
      </div>
      <div v-else>
        <CompanyList
          v-model:page="page"
          :companies="companies"
          :meta="meta"
          :list-view="isList"
        />
      </div>
      <div
        v-if="isLoading"
        role="contentinfo"
        aria-label="Loading content of company list"
        class="rounded-20 bg-nio-grey-200/80 w-full h-full absolute top-0 left-0 flex items-start justify-center"
      >
        <Loader class="mt-8" />
      </div>
    </div>
    <router-view />
  </PageContainerWrapper>
</template>

