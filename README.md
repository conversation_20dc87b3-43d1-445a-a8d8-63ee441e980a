# vue-project

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm run dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm run build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
pnpm run test:unit
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm run lint
```

## End-to-end tests

### Prerequisites

Install Playwright browsers by running:

```sh
pnpm playwright install
```

### Single Command

To run all e2e tests including setup and teardown of the API with a single command:

```sh
pnpm run test:e2e:full
```

### Setup once and repeat tests
 
If you would like to repeat test runs without setting up and tearing down the environment each time, you can run the following commands:

```sh
pnpm run test:e2e:setup
```

Then run tests as many times as you like:

```sh
pnpm run test:e2e
```

To clean up the environment:

```sh   
pnpm run test:e2e:cleanup
```

#### Customize API setup

You can customize the setup of the API project during e2e tests by setting the following variables in your `.env` file:

- `NIO_API_GIT_CLONE_DIR`: The directory where the API is cloned. The default is `./api`.
- `NIO_API_GIT_BRANCH` The branch of the API that is cloned. The default is `develop`.


## Security

This project includes automated security dependency scanning using [retire.js](https://retirejs.github.io/retire.js/) to identify known vulnerabilities in JavaScript dependencies.

### Security Scripts

#### Local Development

```bash
# Basic security scan with JSON output
pnpm run security:scan

# Verbose security scan for detailed analysis
pnpm run security:scan:verbose

# CI/CD optimized scan (exits with code 1 on high severity vulnerabilities)
pnpm run security:scan:ci
```

#### Script Details

- **`security:scan`**: Performs a comprehensive scan and generates a JSON report at `./security-reports/retire-report.json`. Does not fail the process when vulnerabilities are found.
- **`security:scan:verbose`**: Provides detailed output showing all scanned files and identified libraries for debugging purposes.
- **`security:scan:ci`**: Optimized for CI/CD pipelines - exits with code 1 when high severity vulnerabilities are detected, causing the pipeline to fail.

### Configuration Files

- **`.retirejs.json`**: Main configuration file containing severity settings, output paths, scan paths, and ignore list for known acceptable vulnerabilities.
- **`.retireignore`**: File for ignoring specific files or directories during security scanning.

### Output Files

- `security-reports/retire-report.json`: Detailed JSON report with all identified vulnerabilities
- Console output: Human-readable summary for developers

### Troubleshooting

**Common Issues:**

1. **Exit code 1 in CI/CD**: High severity vulnerabilities found
  - Review `security-reports/retire-report.json`
  - Update affected dependencies
  - Add to ignore list with proper justification if necessary

2. **Missing report files**: Ensure `security-reports/` directory exists
  - Run `mkdir -p security-reports` if needed

3. **Slow scanning**: For large projects, configure specific paths in `.retirejs.json`

