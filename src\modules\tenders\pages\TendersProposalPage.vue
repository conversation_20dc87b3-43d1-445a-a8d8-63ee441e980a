<template>
  <PageContainerWrapper>
    <div class="grid lg:grid-cols-[4fr_1.5fr] gap-4">
      <PageCard>
        <TenderHeader v-if="tender" :tender="tender" @updated-tender="onUpdatedTender" />
        <TenderHeaderSkeleton v-else />
      </PageCard>

      <PageCard title="Overview">
        <TenderProposalsOverviewWidget />
      </PageCard>
    </div>

    <PageCard title="Proposals" class="mt-3 min-h-[240px]">
      <div v-if="fetchingInvitedCompanies" class="w-full animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <div v-for="i in 3" :key="i" class="w-full h-[150px] bg-nio-grey-100 rounded-10" />
      </div>
      <TenderProposalsWidget v-else-if="invitedCompanies.length" :invited-companies="pagedCompanies" />
      <template v-else>
        <p class="text-nio-grey-600 w-full text-center">
          Invite at least one company to see proposals.
        </p>
      </template>
      <div v-if="invitedCompanies?.length > itemsPerPage" class="mt-3 mb-0 mx-auto nio-pagination">
        <NioPagination v-model="currentPage" :items-length="invitedCompanies.length" :items-per-page="itemsPerPage" />
      </div>
    </PageCard>

    <PageCard title="Candidates" class="mt-3">
      <TenderCandidates :tender="tender" :tender-id="route.params.id as string" />
    </PageCard>
  </PageContainerWrapper>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref } from 'vue';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import type { Tender } from '@/modules/tenders/types/tenders-types';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import TenderHeader from '../components/TenderDetail/TenderHeader.vue';
import PageCard from '@/common/components/PageCard.vue';
import { useRoute } from 'vue-router';
import TenderCandidates from '../components/tender-candidates/TenderCandidates.vue';
import TenderProposalsOverviewWidget from '../components/TenderProposalsOverviewWidget.vue';
import TenderProposalsWidget from '@/modules/tenders/components/TenderProposalsWidget.vue';
import TenderHeaderSkeleton from '../components/TenderHeaderSkeleton.vue';
import { handleTenderError } from '@/modules/tenders/handlers/error';
import NioPagination from '@/common/components/NioPagination.vue';

const route = useRoute();
const tenderStore = useTenderStore();
const tender = ref<Tender | undefined>();
const fetchingInvitedCompanies = ref(true);
const currentPage = ref(1);
const itemsPerPage = (import.meta.env.MODE === 'test' ? 2 : 9);

const invitedCompanies = computed(() => tenderStore.invitedVendors);

const pagedCompanies = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  return tenderStore.invitedVendors.slice(start, start + itemsPerPage);
});

async function fetchTenderAndCompanies(id: string) {
  try {
    fetchingInvitedCompanies.value = true;
    const response = await tenderStore.fetchTenderDetail(id);
    tender.value = response;
    await tenderStore.fetchInvitedCompanies(id);
  } catch(error) {
    handleTenderError(error);
  } finally {
    fetchingInvitedCompanies.value = false;
  }
}

const onUpdatedTender = (updatedTender: Tender) => { tender.value = updatedTender; };

onBeforeMount(async() => {
  tenderStore.resetInvitedCompanies();
  await fetchTenderAndCompanies(route.params.id as string);
});
</script>
