<script setup lang="ts">
import { computed, onBeforeMount, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import CompaniesMatchingTable from '@/modules/tenders/components/CompaniesMatchingTable.vue';
import CompaniesMatchingProject from '@/modules/tenders/components/CompaniesMatchingProject.vue';
import MatchingTopCard from '@/modules/tenders/components/tender-matching/MatchingTopCard.vue';
import MatchingSkeleton from '@/modules/tenders/components/tender-matching/MatchingSkeleton.vue';
import { toast } from '@/common/utils/NotificationService';
import { CompaniesFilter, MatchingStatus } from '@/modules/tenders/types/tenders-enums';
import TenderInviteDrawer from '@/modules/tenders/components/TenderInviteDrawer.vue';
import MatchingLoadingText from '@/modules/tenders/components/MatchingLoadingText.vue';

const tenderStore = useTenderStore();
const route = useRoute();

const loading = ref(true);
const invitedCompanyIds = computed(() => tenderStore.invitedVendorIds);
const expandedCompanyIds = ref<string[]>([]);
const matchingResponse = ref<any>(null);

const inviteVisible = ref(false);

const topCompanies = computed(() => matchingResponse.value?.topVendors ?? matchingResponse.value?.companies ?? []);
const otherCompanies = computed(() => matchingResponse.value?.otherVendors ?? []);
const dataReady = computed(() => topCompanies.value.length > 0 || otherCompanies.value.length > 0);
const matchId = computed(() => tenderStore.specificMatchingResult?.id ?? '');
const matchingStatus = computed(() => tenderStore.matchingStatus[CompaniesFilter.Selection]);

const projectDetailModal = reactive({
  isOpened: false,
  detail: undefined as any,
});

const emit = defineEmits(['matching-loaded']);

const handleCompanyInvite = async(id: string) => {
  try {
    const tenderId = route.params.id as string;
    await tenderStore.inviteCompany(tenderId, id);
  } catch {
    toast.show('Error', 'Something went wrong with company invitation.', 'error');
  }
};

const handleCompanyUninvite = async(id: string) => {
  try {
    const tenderId = route.params.id as string;
    await tenderStore.uninviteCompany(tenderId, id);
  } catch {
    toast.show('Error', 'Something went wrong with company uninvitation.', 'error');
  }
};

const toggleCompanyExpanded = (id: string) => {
  if (expandedCompanyIds.value.includes(id)) {
    expandedCompanyIds.value = expandedCompanyIds.value.filter(c => c !== id);
  } else {
    expandedCompanyIds.value.push(id);
  }
};

const onShowProjectDetail = (companyId: number) => {
  const company = [...topCompanies.value, ...otherCompanies.value].find(c => c?.id === companyId);
  if (!company) {
    toast.show('Company not found', 'We were not able to find the requested company.', 'error');
    return;
  }

  projectDetailModal.detail = {
    id: company.id,
    name: company.name,
    country: company.country ?? '',
    created_at: company.created_at ?? '',
    about: company.about ?? '',
    industries: company.industries ?? [],
    technologies: company.technologies ?? [],
    matching: company.matching ?? { score: 0, tech_score: 0 },
  };

  projectDetailModal.isOpened = true;
};

onBeforeMount(async() => {
  const id = route.params.id as string;
  const [matching] = await Promise.all([
    tenderStore.startMatchingPoll(id, 5000, CompaniesFilter.Selection),
    tenderStore.fetchInvitedCompanies(id),
  ]);
  matchingResponse.value = matching;
  loading.value = false;
  emit('matching-loaded');
});
</script>

<template>
  <div v-if="matchingStatus === MatchingStatus.InProgress" class="min-h-[250px] flex items-center justify-center">
    <MatchingLoadingText />
  </div>
  <div v-else class="w-full transition-all duration-500 ease-[var(--ease-out-3)]">
    <transition mode="out-in" :duration="400">
      <div v-if="matchingStatus === MatchingStatus.Failed || matchingStatus === MatchingStatus.NoVendorsMatched">
        <MatchingSkeleton :error="true" :matching-status="matchingStatus" />
      </div>
      <div v-else-if="!loading && dataReady" class="grid gap-4 w-full xl:grid-cols-2">
        <MatchingTopCard
          v-for="(company, index) in topCompanies"
          :key="company?.id"
          :company="company"
          :index="index"
          :tender-id="route.params.id as string"
          :invited="invitedCompanyIds.includes(company.id)"
          @invite="handleCompanyInvite"
          @uninvite="handleCompanyUninvite"
        />
      </div>
      <MatchingSkeleton v-else />
    </transition>

    <div v-if="otherCompanies?.length > 0" class="mt-5">
      <CompaniesMatchingTable
        :companies="otherCompanies"
        :starting-index="topCompanies.length + 1"
        :size="5"
        :tender-public-id="route.params.id as string"
        :match-id="matchId"
        type="top"
        :loading="loading"
        :expanded-company-ids="expandedCompanyIds"
        :invited-company-ids="invitedCompanyIds"
        @invite="handleCompanyInvite"
        @uninvite="handleCompanyUninvite"
        @show-project-detail="onShowProjectDetail"
        @toggle-expand="toggleCompanyExpanded"
      />
    </div>

    <CompaniesMatchingProject
      v-model="projectDetailModal.isOpened"
      :project-detail="projectDetailModal.detail"
    />

    <TenderInviteDrawer v-model:visible="inviteVisible" />
  </div>
</template>
