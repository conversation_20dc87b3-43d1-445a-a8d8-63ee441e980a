import type { RateType } from '@/common/composables/useHourlyRate';

export interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in?: number;
}

export enum CompanyCategory {
  ENTERPRISE = 'enterprise',
  AGENCY = 'agency',
  SOFTWARE_HOUSE = 'software_house',
}

export enum CompanyStatus {
  DRAFT = 'draft',
  AWAITING_APPROVAL = 'awaiting_approval',
  PUBLISHED = 'published',
}

export interface ProfileResponse {
  name: string;
  surname: string;
  email: string;
  email_verified: boolean;
  phone: string | null;
  position: string;
  roles: string[];
  company: {
    id: string;
    name: string;
    is_vendor: boolean;
    vendor_id: string;
  };
  tenders?: boolean;
  workspaces: {
    name: string | null;
    companies: {
      id: string;
      name: string;
      country: string;
      headquarters: string;
      category: CompanyCategory;
      status: CompanyStatus;
    }[];
    rate_type: RateType;
    marketplace_disabled: boolean | null;
  }[];
}
