<script setup lang="ts">
import Drawer from 'primevue/drawer';
import TenderInvitePanel from '@/modules/tenders/components/TenderInvitePanel.vue';
import CloseIcon from '@/assets/icons/close-icon.svg';

defineProps<{
  visible: boolean;
}>();
const emit = defineEmits(['update:visible']);
</script>

<template>
  <Drawer
    v-if="visible"
    :visible="visible"
    position="right"
    :show-close-icon="false"
    :block-scroll="false"
    :dismissable="false"
    :modal="false"
    class="w-[1050px] h-full bg-white relative p-5 shadow-lg z-50 flex flex-col rounded-l-30"
    @update:visible="val => emit('update:visible', val)"
  >
    <div
      class="absolute cursor-pointer top-[10px] -left-[40px] w-[30px] h-[30px] bg-white border border-nio-blue-400 rounded-full flex items-center justify-center shadow-lg hover:bg-nio-blue-100 transition-colors"
      data-close-btn
      role="button"
      aria-label="Close search"
      @click="emit('update:visible', false)"
    >
      <CloseIcon
        class="w-[13px] h-[13px]"
      />
    </div>
    <TenderInvitePanel />
  </Drawer>
</template>
