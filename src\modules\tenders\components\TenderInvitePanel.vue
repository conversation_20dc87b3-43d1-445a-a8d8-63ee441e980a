<script setup lang="ts">
import { ref, computed, onMounted, inject, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { useQuestionStore } from '@/modules/companies/stores/question-store';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import TenderInviteTable from '@/modules/tenders/components/TenderInviteTable.vue';
import EmailInviteModal from './EmailInviteModal.vue';
import { toast } from '@/common/utils/NotificationService';
import { CompanyCategory } from '@/modules/auth/types/auth-types';
import { normalizeSearch } from '@/common/utils/strings';
import MagnifyingGlassIcon from '@/assets/icons/magnifying-glass-icon.svg';
import { XIcon } from 'lucide-vue-next';
import type { Tender } from '../types/tenders-types';
import { CustomEvents } from '@/common/utils/custom-events';

const route = useRoute();
const tenderId = route.params.id as string;

const questionStore = useQuestionStore();
const tenderStore = useTenderStore();

const allVendors = ref<any[]>([]);
const invitedVendors = computed(() => tenderStore.invitedVendors);
const invitedVendorIds = computed(() => tenderStore.invitedVendorIds);

const searchQuery = ref('');
const selectedFilter = ref<'all' | 'agency' | 'enterprise'>('all');

const availableVendors = computed(() => {
  let vendors = allVendors.value.filter((v: any) => !invitedVendorIds.value.includes(v.id));

  if (selectedFilter.value !== 'all') {
    vendors = vendors.filter((v: any) => v.category === selectedFilter.value);
  }

  if (searchQuery.value.trim()) {
    const normalizedSearch = normalizeSearch(searchQuery.value);
    vendors = vendors.filter((v: any) => {
      const normalizedName = normalizeSearch(v.name);
      return normalizedName.includes(normalizedSearch);
    });
  }

  return vendors;
});

const invitedVendorsTableData = computed(() => {
  let vendors = invitedVendors.value;

  if (selectedFilter.value !== 'all') {
    vendors = vendors.filter((v: any) => v.category === selectedFilter.value);
  }

  if (searchQuery.value.trim()) {
    const normalizedSearch = normalizeSearch(searchQuery.value);
    vendors = vendors.filter((v: any) => {
      const normalizedName = normalizeSearch(v.name);
      return normalizedName.includes(normalizedSearch.replace(/\*/g, ''));
    });
  }

  return vendors.map((v: any) => ({
    id: v.id,
    name: v.name,
    country: v.country ?? '',
    city: v.hq ?? v.headquarters ?? '',
    match: v.match_details?.overall_score ?? v.score ?? 0,
    employee_emails: v.employee_emails ?? [],
    sent_at: v.sent_at ?? null,
    category: v.category,
  }));
});

const allInvitesSent = computed(() =>
  invitedVendors.value.length > 0 && invitedVendors.value.every((v: any) => !!v.sent_at)
);

const filterOptions = computed<{
  value: 'all' | 'agency' | 'enterprise';
  label: string;
  count: number;
  badge?: { text: string; bgClass: string; textClass: string };
  selectedBg?: string;
}[]>(() => {
  const allVendorsCount = allVendors.value.length + invitedVendors.value.length;
  const agencyCount = [...allVendors.value, ...invitedVendors.value].filter(v => v.category === CompanyCategory.AGENCY).length;
  const enterpriseCount = [...allVendors.value, ...invitedVendors.value].filter(v => v.category === CompanyCategory.ENTERPRISE).length;

  return [
    {
      value: 'all',
      label: 'All',
      count: allVendorsCount
    },
    {
      value: 'agency',
      label: 'Agency',
      count: agencyCount,
      selectedBg: 'bg-nio-green-text text-white',
      badge: {
        text: 'A',
        bgClass: 'bg-nio-green-text/20',
        textClass: 'text-green-700'
      }
    },
    {
      value: 'enterprise',
      label: 'Enterprise',
      count: enterpriseCount,
      selectedBg: 'bg-nio-blue-500 text-white',
      badge: {
        text: 'E',
        bgClass: 'bg-nio-blue-500/15',
        textClass: 'text-nio-blue-800'
      }
    },
  ];
});

const availableVendorsTableData = computed(() =>
  availableVendors.value
    .map((v: any) => ({
      id: v.id,
      name: v.name,
      country: v.country ?? '',
      city: v.hq ?? v.headquarters ?? '',
      match: v.match_details?.overall_score ?? v.score ?? 0,
      employee_emails: v.employee_emails ?? [],
      category: v.category,
    }))
    .toSorted((a, b) => a.name.localeCompare(b.name))
);

const removeVendor = async(id: string) => {
  await tenderStore.uninviteCompany(tenderId, id);
};

const inviteVendor = async(id: string) => {
  await tenderStore.inviteCompany(tenderId, id);
};

const showEmailModal = ref(false);

const closeInviteSidebar = inject('closeInviteSidebar') as (() => void) | undefined;

const sendInvites = () => {
  showEmailModal.value = true;
};

const handleEmailSend = async() => {
  try {
    await tenderStore.sendInvitedCompanies(tenderId);
    showEmailModal.value = false;
    if (closeInviteSidebar) {
      closeInviteSidebar();
    }
    toast.show('Invites sent', 'Invitations have been successfully sent to all selected vendors.', 'success');
  } catch (error) {
    console.error('Failed to send invites:', error);
    toast.show('Error', 'Failed to send invitations. Please try again.', 'error');
  }
};

const handleEmailClose = () => {
  showEmailModal.value = false;
};

const isLoading = ref(true);
const tenderName = ref('');
const tenderData = ref<Tender | null>(null);

async function fetchData() {
  isLoading.value = true;
  const resp = await questionStore.getVendors();
  allVendors.value = Object.values(resp);
  const tenderDetail = await tenderStore.fetchTenderDetail(tenderId);
  tenderName.value = tenderDetail.name;
  tenderData.value = tenderDetail;
  await tenderStore.fetchInvitedCompanies(tenderId);
  isLoading.value = false;
}

onMounted(fetchData);

const onUpdatedTender = (tender: Event) => {
  const { detail } = tender as CustomEvent<{ updatedTender: Tender }>;
  tenderData.value = detail.updatedTender;
};

window.eventBus.addEventListener(CustomEvents.TenderDetailUpdated, onUpdatedTender);

onBeforeUnmount(() => {
  window.eventBus.removeEventListener(CustomEvents.TenderDetailUpdated, onUpdatedTender);
});
</script>

<template>
  <div class="w-full h-[95vh]">
    <div class="flex flex-col h-full max-h-full">
      <div class="mb-6 h-fit">
        <div class="flex items-center justify-between gap-4 flex-wrap">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600">Filter:</span>
            <div class="flex gap-2">
              <button
                v-for="option in filterOptions"
                :key="option.value"
                class="px-3 py-1.5 rounded-full text-sm transition-all duration-200 flex items-center gap-1.5 cursor-pointer"
                :class="selectedFilter === option.value
                  ? (option.selectedBg ?? 'bg-blue-500 text-white')
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                @click="selectedFilter = option.value"
              >
                <span v-if="option.badge" class="flex items-center">
                  <span
                    class="inline-block px-1 py-px rounded-sm"
                    :class="[option.badge.bgClass, option.badge.textClass]"
                  >
                    {{ option.badge.text }}
                  </span>
                  <span class="inline-block ml-1">{{ option.label }}</span>
                </span>
                <span v-else>{{ option.label }}</span>
                <span class="text-xs">{{ option.count }}</span>
              </button>
            </div>
          </div>

          <div class="relative">
            <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="w-4 h-4 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search..."
              class="w-64 h-9 pl-10 pr-10 py-2 bg-gray-100 text-gray-700 text-sm rounded-full border border-gray-300/40 focus:outline-none focus:ring-0 focus:bg-white transition-all duration-200 hover:bg-gray-50"
            >
            <button
              v-if="searchQuery"
              class="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
              @click="searchQuery = ''"
            >
              <XIcon class="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>

      <div class="flex gap-8 flex-1 min-h-0">
        <div class="flex-1 min-w-0 flex flex-col">
          <div class="flex items-center justify-between mb-4 flex-shrink-0">
            <h1 class="text-h4 font-normal text-nio-black leading-normal tracking-[0.72px]">
              Invites
            </h1>
          </div>
          <div class="p-2 bg-white rounded-20 mb-4 flex-1 min-h-0 border border-nio-blue-outline-stroke-400 overflow-y-auto" role="list" aria-label="Invites">
            <TenderInviteTable
              :vendors="invitedVendorsTableData"
              :starting-index="1"
              :loading="isLoading"
              placeholder-text="No vendors have been invited yet."
              @remove="removeVendor"
            />
          </div>
          <div v-show="!isLoading" class="flex justify-center flex-shrink-0">
            <button
              v-if="invitedVendorsTableData.length && !allInvitesSent"
              class="px-6 py-3 w-full flex items-center justify-center gap-2 bg-nio-blue-800 text-nio-white text-[16px] rounded-50 hover:bg-nio-blue-600-hover transition-colors cursor-pointer focus:outline-none outline-none"
              @click="sendInvites"
            >
              <div class="flex items-center justify-center rounded-full bg-white w-6 h-6">
                <img
                  src="@/assets/icons/add-to-list.png"
                  alt="Add to list"
                  width="10"
                  height="10"
                >
              </div>

              Send invites
            </button>
          </div>
        </div>
        <div class="flex flex-col flex-1 min-w-0">
          <div class="flex items-center justify-between mb-4 flex-shrink-0">
            <h1 class="text-h5 font-normal text-nio-black leading-normal tracking-[0.72px]">
              Strategic Partners
            </h1>
          </div>
          <div class="p-2 flex-1 min-h-0 bg-white rounded-20 border border-nio-blue-outline-stroke-400 overflow-y-auto" role="list" aria-label="Strategic partners">
            <TenderInviteTable
              :vendors="availableVendorsTableData"
              :starting-index="1"
              :loading="isLoading"
              action-label="Invite"
              placeholder-text="No available vendors in Vendor Hub."
              @remove="inviteVendor"
            />
          </div>
        </div>
      </div>
      <EmailInviteModal
        :visible="showEmailModal"
        :tender-name="tenderName"
        :tender-data="tenderData!"
        @close="handleEmailClose"
        @send="handleEmailSend"
      />
    </div>
  </div>
</template>

