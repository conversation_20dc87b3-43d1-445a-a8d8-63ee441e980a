<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { nioAxios } from '@/axios';
import { CandidateStatus } from '@/modules/tenders/types/tenders-types';
import { statusOptions } from '@/common/utils/candidate-status';
import DetailPopover01 from '@/common/components/popover/DetailPopover01.vue';
import NioDropdown from '@/common/components/UI/NioDropdown.vue';
import type { DynamicFormItemData } from '@/common/utils/forms';
import { toast } from '@/common/utils/NotificationService';

interface Props {
  tenderId: string;
  candidateId: string;
  candidateName: string;
  currentStatus: CandidateStatus;
}

const props = defineProps<Props>();
const emit = defineEmits(['statusUpdated']);

const submitting = ref(false);

const inputData = reactive<DynamicFormItemData>({
  name: 'status',
  component: {
    id: 'select',
    payload_key: 'status',
    options: statusOptions
  }
});

const isPopoverOpen = defineModel<boolean>();
const selectedValue = ref<CandidateStatus>(props.currentStatus);

const onSubmit = async() => {
  if (submitting.value) {
    return;
  }
  submitting.value = true;
  try {
    await nioAxios.put(`/enterprise/tenders/${props.tenderId}/candidates/${props.candidateId}/application`, {
      status: selectedValue.value
    });

    toast.show('Status updated', `Successfully updated status to ${inputData.component.options?.find(opt => opt.value === selectedValue.value)?.label}.`, 'success');
    emit('statusUpdated', selectedValue.value);
    isPopoverOpen.value = false;
  } catch (error) {
    console.error('Failed to update candidate status:', error);
    toast.show('An error occurred', 'Failed to update candidate status.', 'error');
  } finally {
    submitting.value = false;
  }
};

watch(() => props.currentStatus, () => {
  selectedValue.value = props.currentStatus;
});
</script>

<template>
  <DetailPopover01
    v-model="isPopoverOpen"
    width="1100px"
    min-height="auto"
    height="fit-content"
    title="Update Status"
  >
    <form class="h-full px-4 py-2 overflow-y-visible" @submit.prevent="onSubmit">
      <div class="mb-4">
        <div class="flex items-center gap-3">
          <label for="status-input" class="block text-sm font-medium text-nio-grey-700 ml-0.5">
            Status
          </label>
          <NioDropdown
            v-model="selectedValue"
            :input-data="inputData"
            input-id="status-input"
            class="w-60"
            data-testid="status-dropdown"
          />
        </div>
      </div>
      <hr class="mt-4">
      <button
        type="submit"
        :disabled="submitting"
        class="cursor-pointer py-3 px-[14px] bg-nio-blue-800 hover:bg-nio-blue-600-hover text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px] disabled:opacity-50 disabled:cursor-not-allowed mt-4"
        data-testid="submit-status-button"
      >
        {{ submitting ? 'Saving...' : 'Submit' }}
      </button>
    </form>
  </DetailPopover01>
</template>
