.p-togglebutton {
    @apply inline-flex items-center justify-center overflow-hidden relative cursor-pointer select-none
        border border-surface-100 dark:border-surface-950 rounded-md
        bg-surface-100 dark:bg-surface-950
        text-surface-500 dark:text-surface-400 text-base font-medium
        px-4 py-2
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary
        disabled:opacity-100 disabled:cursor-default
        disabled:bg-surface-200 disabled:border-surface-200 disabled:text-surface-500
        disabled:dark:bg-surface-700 disabled:dark:border-surface-700 disabled:dark:text-surface-400
        transition-colors duration-300
        before:bg-transparent before:absolute before:start-1 before:top-1 before:rounded-md before:w-[calc(100%-0.5rem)] before:h-[calc(100%-0.5rem)]
        before:transition-colors before:duration-200
}

.p-togglebutton-content {
    @apply relative inline-flex items-center justify-center gap-2
}

.p-togglebutton-label,
.p-togglebutton-icon {
    @apply relative transition-none
}

.p-togglebutton.p-togglebutton-checked::before {
    @apply bg-surface-0 dark:bg-surface-800 shadow-[0px_1px_2px_0px_rgba(0,0,0,0.02),0px_1px_2px_0px_rgba(0,0,0,0.04)]
}

.p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover {
    @apply bg-surface-100 dark:bg-surface-950 text-surface-700 dark:text-surface-300
}

.p-togglebutton.p-togglebutton-checked {
    @apply bg-surface-100 dark:bg-surface-950 border-surface-100 dark:border-surface-950 text-surface-900 dark:text-surface-0
}

.p-togglebutton.p-invalid {
    @apply border-red-400 dark:border-red-300
}

.p-togglebutton-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover .p-togglebutton-icon {
    @apply text-surface-700 dark:text-surface-300
}

.p-togglebutton.p-togglebutton-checked .p-togglebutton-icon {
    @apply text-surface-900 dark:text-surface-0
}

.p-togglebutton:disabled .p-togglebutton-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-togglebutton-sm {
    @apply text-sm px-[0.75rem] py-[0.375rem]
}

.p-togglebutton-lg {
    @apply text-lg px-[1.25rem] py-[0.625rem]
}
