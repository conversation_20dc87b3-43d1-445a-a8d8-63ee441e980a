import { type Locator, type Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class TendersPage extends BasePage {
  readonly urlPath: string = '/tenders';
  readonly tendersList: Locator;
  readonly tenderItems: Locator;

  constructor(page: Page) {
    super(page);
    this.tendersList = page.getByRole('list', { name: 'Tenders list' });
    this.tenderItems = this.tendersList.getByRole('listitem');
  }

  getTenderCard(tenderName: string): Locator {
    return this.tenderItems.filter({ hasText: tenderName });
  }

  getTenderCardRatePill(tenderName: string): Locator {
    return this.getTenderCard(tenderName).getByText(/€\d+/);
  }

  async goto() {
    await this.page.goto('/');
    await this.mainMenuToggleButton.click();
    await this.navigation.getByRole('link', { name: 'Tenders' }).click();
    await this.page.waitForURL(this.urlPath);
  }

  async openTender(tenderName: string) {
    await this.getTenderCard(tenderName).getByRole('link', { name: 'Open' }).click();
    await this.page.waitForURL(/tenders\/[^/]+\/detail/);
  }
}
