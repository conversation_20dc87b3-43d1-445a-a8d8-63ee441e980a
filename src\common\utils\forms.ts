import { defineAsyncComponent } from 'vue';
import type { AxiosError } from 'axios';

export const FormTypeMap = {
  'standard': defineAsyncComponent(() => import('@/common/components/forms/FormStandardRoot.vue')),
  'collection': defineAsyncComponent(() => import('@/common/components/forms/FormCollectionRoot.vue')),
};

export const FormComponentMap = {
  'input-text': defineAsyncComponent(() => import('@/common/components/UI/NioInput.vue')),
  'select': defineAsyncComponent(() => import('@/common/components/UI/NioDropdown.vue')),
  'multi-select': defineAsyncComponent(() => import('@/common/components/UI/NioDropdown.vue')),
  'date-single': defineAsyncComponent(() => import('@/common/components/UI/NioDateInputSingle.vue')),
  'date-range': defineAsyncComponent(() => import('@/common/components/UI/NioDateInput.vue')),
  'textarea': defineAsyncComponent(() => import('@/common/components/UI/NioMultilineInput.vue')),
  'number': defineAsyncComponent(() => import('@/common/components/UI/NioInputNumber.vue')),
  'slider': defineAsyncComponent(() => import('@/common/components/UI/NioSlider.vue')),
  'number-range': defineAsyncComponent(() => import('@/common/components/UI/NioInputNumberRange.vue')),
};

export const FormReactiveTypeMap: Record<keyof typeof FormComponentMap, (componentDetail?: DynamicFormItemData['component']) => any> = {
  'input-text': () => '',
  'select': () => '',
  'multi-select': () => [],
  'date-range': () => ({ start_date: '', end_date: '' }),
  'date-single': () => '',
  'textarea': () => '',
  'number': () => 0,
  'slider': () => 0,
  'number-range': componentDetail => [componentDetail?.min ?? undefined, componentDetail?.max ?? undefined],
};

export type DynamicFormStructure<T = FormStandardData | FormCollectionData> = {
  form: keyof typeof FormTypeMap,
  data: T
}

export type FormSelectOption = {
  label: string,
  value: string | number,
  disabled?: boolean,
  description?: string,
}

export type DynamicFormItemData = {
  name: string,
  value?: string | { start_date: string | null, end_date: string | null } | string[],
  component: {
    id: keyof typeof FormComponentMap,
    payload_key: string,
    min?: number,
    max?: number,
    default_value?: any,
    selected_options?: {
      label: string,
      value: string | number,
    }[],
    options?: FormSelectOption[],
    options_endpoint?: {
      url: string,
      mapping: {
        label: string,
        value: string,
      }
    },
    options_from?: {
      payload_key: string,
      label: string,
    },
  },
}

export type FormStandardData = DynamicFormItemData[];

export type FormCollectionData = {
  id: string|null,
  suggested?: boolean,
  body: DynamicFormItemData[],
}[]

export type FormErrors = {
  payload_key: string,
  message?: string,
  subKeys?: Record<string, string>,
}[]

// Suggested resources types
export type SuggestedTechnology = {
  id: string,
  name: string,
}

export type SuggestedTool = {
  id: string,
  name: string,
}

export type SuggestedResourceItem = {
  job_title: string,
  technologies: SuggestedTechnology[],
  tools: SuggestedTool[],
}

export type SuggestedResourcesData = SuggestedResourceItem[]

export type SuggestedResourcesObject = {
  data: SuggestedResourcesData,
  isLoading: boolean,
}

export const transformValidationErrorsForCollection = (
  e: AxiosError<{ error: { errors: Record<string, string[]> } }>,
  prefix: string
): Record<number, FormErrors> => {
  const regex = new RegExp(`^${prefix}\\.(\\d+)\\.(.+)$`);

  return Object.entries(e.response!.data.error.errors).reduce(
    (acc: Record<number, FormErrors>, [key, messages]) => {
      const match = key.match(regex);

      if (!match) {return acc;}

      const resourceIndex = parseInt(match[1], 10);
      const fieldPath = match[2]; // could be 'description' or 'timeline.end_date'
      const parts = fieldPath.split('.');

      if (!acc[resourceIndex]) {
        acc[resourceIndex] = [];
      }

      if (parts.length === 1) {
        acc[resourceIndex].push({
          payload_key: parts[0],
          message: messages[0]
        });
      } else {
        const [groupKey, subKey] = parts;
        let existing = acc[resourceIndex].find(item => item.payload_key === groupKey);

        if (!existing) {
          existing = {
            payload_key: groupKey,
            subKeys: {}
          };
          acc[resourceIndex].push(existing);
        }

        if (!existing.subKeys) {
          existing.subKeys = {};
        }

        existing.subKeys[subKey] = messages[0];
      }
      return acc;
    },
    {} as Record<number, FormErrors>
  );
};

export const transformValidationErrorsForStandardForm = (
  e: AxiosError<{ error: { errors: Record<string, string[]> } }>,
  prefix: string
) => {
  const regex = new RegExp(`^${prefix}\\.`);

  const grouped: Record<string, any> = {};

  Object.entries(e.response!.data.error.errors).forEach(([key, messages]) => {
    const cleanedKey = key.replace(regex, ''); // e.g., "timeline.end_date" or "description"
    const parts = cleanedKey.split('.');

    if (parts.length === 1) {
      // Simple field: e.g., "description"
      grouped[parts[0]] = {
        payload_key: parts[0],
        message: messages[0],
      };
    } else {
      // Nested field: e.g., "timeline.end_date"
      const [groupKey, subKey] = parts;

      if (!grouped[groupKey]) {
        grouped[groupKey] = {
          payload_key: groupKey,
          subKeys: {},
        };
      }

      grouped[groupKey].subKeys[subKey] = messages[0];
    }
  });

  return Object.values(grouped);
};

export type PromiseWithResolvers = {
  promise: Promise<any>,
  resolve: (...args: any[]) => void,
  reject: (...args: any[]) => void,
}

export type BlurPromise = PromiseWithResolvers
