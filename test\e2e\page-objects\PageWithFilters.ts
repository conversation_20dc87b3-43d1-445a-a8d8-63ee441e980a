import { expect, Locator, Page } from '@playwright/test';
import { BasePage } from './BasePage';

export abstract class PageWithFilters extends BasePage {

  readonly listItems: Locator;

  constructor(page: Page, listItems: Locator) {
    super(page);
    this.listItems = listItems;
  }

  async waitForResponseDecorator(triggerFetchCallback: () => Promise<void>) {
    const responsePromise = this.page.waitForResponse(
      res => {
        return /[?&]filter(?:\[[^\]]*\])*=/.test(res.url());
      });
    await triggerFetchCallback();
    await responsePromise;
  }

  async fillTextFilter(filterLocator: Locator, searchText: string) {
    const searchInput = filterLocator.getByRole('textbox');
    await this.waitForResponseDecorator(async() => await searchInput.fill(searchText));
  }

  async selectDropdownFilter(filterLocator: Locator) {
    await filterLocator.click();
    const dropdownContainer = filterLocator.getByRole('list', { name: 'Dropdown options list' });
    await expect(dropdownContainer).toBeVisible();
    const firstOption = dropdownContainer.getByRole('listitem', { name: 'Dropdown option' }).first();
    await expect(firstOption).toBeVisible();
    await this.waitForResponseDecorator(async() => await firstOption.click());

    // Click on filter to close dropdown
    await filterLocator.click();
    await expect(dropdownContainer).toBeHidden();
  }

  async fillNumberFilter(filterLocator: Locator, value: string) {
    const numberInput = filterLocator.getByRole('spinbutton');
    await this.waitForResponseDecorator(async() => await numberInput.fill(value));
  }

  async fillDateFilter(filterLocator: Locator, date: string) {
    const dateInput = filterLocator.getByRole('textbox');
    await this.waitForResponseDecorator(async() => await dateInput.fill(date));
  }

  async getConsultantsCount(): Promise<number> {
    return await this.listItems.count();
  }

  async clearAllFilters(filterLocator: Locator) {
    const currentUrl = this.page.url();
    await this.page.goto(currentUrl);
    await this.listItems.count(); // Wait for page to load
    await expect(filterLocator).toBeVisible({ timeout: 15000 });
  }

  async testDropdownFilter(initialCount: number, filterLocator: Locator) {
    await this.selectDropdownFilter(filterLocator);
    const filteredCount = await this.getConsultantsCount();
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
    await this.clearAllFilters(filterLocator);
  }

  async testNumberFilter(initialCount: number, filterLocator: Locator, value: string) {
    await this.fillNumberFilter(filterLocator, value);
    const filteredCount = await this.getConsultantsCount();
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
    await this.clearAllFilters(filterLocator);
  }

  async testDateFilter(initialCount: number, filterLocator: Locator, date: string) {
    await this.fillDateFilter(filterLocator, date);
    const filteredCount = await this.getConsultantsCount();
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
    await this.clearAllFilters(filterLocator);
  }

  async testTextFilter(initialCount: number, filterLocator: Locator, value: string) {
    await this.fillTextFilter(filterLocator, value);
    const filteredCount = await this.getConsultantsCount();
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
    await this.clearAllFilters(filterLocator);
  }
}
