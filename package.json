{"name": "nio-metch-client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "run-p type-build \"build-only {@}\" --", "build-only": "vite build", "dev": "vite", "lint": "eslint --fix . && pnpm type-check", "lint:ci": "eslint --format gitlab . && pnpm type-check", "preview": "vite preview", "test:e2e": "playwright test", "test:e2e:cleanup": "node --env-file .env ./scripts/e2e-tests-cleanup.js", "test:e2e:full": "pnpm test:e2e:setup && pnpm test:e2e; exitCode=$?; pnpm test:e2e:cleanup; exit $exitCode", "test:e2e:setup": "node --env-file .env ./scripts/e2e-tests-setup.js", "test:e2e:start-api": "node --env-file .env ./scripts/e2e-tests-start-api.js", "test:install-browsers": "pnpm playwright install", "security:scan": "retire --outputformat json --outputpath ./security-reports/retire-report.json || true", "security:scan:ci": "retire --severity high --outputformat json --outputpath security-reports/retire-report.json --exitwith 1", "security:scan:verbose": "retire --verbose", "test:unit": "vitest", "type-build": "vue-tsc --build --force", "type-check": "vue-tsc --build --noEmit"}, "dependencies": {"@microsoft/clarity": "^1.0.0", "@primeuix/themes": "^1.2.3", "@sentry/vite-plugin": "^3.6.1", "@sentry/vue": "^9.46.0", "@tailwindcss/vite": "^4.1.12", "@unhead/vue": "^2.0.14", "@vitejs/plugin-vue": "^5.2.4", "@vueform/slider": "^2.1.10", "@vueuse/core": "^12.8.2", "axios": "^1.12.2", "globals": "^16.3.0", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.511.0", "luxon": "^3.7.1", "oauth4webapi": "^3.7.0", "open-props": "^1.7.16", "pinia": "^3.0.3", "primevue": "^4.3.7", "retire": "^5.3.0", "tailwindcss-primeui": "^0.5.1", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.7.7", "vite-svg-loader": "^5.1.0", "vue": "^3.5.19", "vue-awesome-paginate": "^1.2.0", "vue-gtag": "^2.1.2", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1", "vue3-lottie": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@playwright/test": "^1.55.0", "@primevue/auto-import-resolver": "^4.3.7", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.7.1", "@types/node": "^22.17.2", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "envsub": "^4.1.0", "eslint": "^9.33.0", "eslint-formatter-gitlab": "^6.0.1", "eslint-plugin-oxlint": "^0.15.15", "eslint-plugin-vue": "^10.4.0", "execa": "^9.6.0", "fs-extra": "^11.3.1", "jsdom": "^26.1.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.15.15", "sass-embedded": "^1.90.0", "tailwindcss": "^4.1.12", "typescript": "~5.7.3", "unplugin-vue-components": "^28.8.0", "vitest": "^3.2.4", "vue-tsc": "^2.2.12"}, "packageManager": "pnpm@10.15.0", "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "@sentry/cli", "@tailwindcss/oxide", "esbuild", "vue-demi"]}}