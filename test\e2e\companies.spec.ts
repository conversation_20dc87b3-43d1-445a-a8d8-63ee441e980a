import { test, expect } from '@playwright/test';
import { CompanyListPage } from './page-objects/companies/CompanyListPage';
import { CompanyDetailPage } from './page-objects/companies/CompanyDetailPage';
import { CompanyUploadPage } from './page-objects/companies/CompanyUploadPage';
import * as path from 'node:path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const fixturesDir = path.join(__dirname, 'fixtures');
const sampleCompaniesCsvFilePath = path.join(fixturesDir, 'companies-ignac-bajza.csv');

let companyListPage: CompanyListPage;
let companyDetailPage: CompanyDetailPage;

const SEARCH_TERM = 'Nimbus';

test.beforeEach(async({ page }) => {
  companyListPage = new CompanyListPage(page);
  companyDetailPage = new CompanyDetailPage(page);
});

test('should load companies page, display vendor cards and filters', async({ page }) => {
  await companyListPage.goto();
  await expect(page).toHaveURL('/companies');
  await expect(companyListPage.vendorCards.first()).toBeVisible();
  const cardCount = await companyListPage.vendorCards.count();
  expect(cardCount).toBeGreaterThan(0);
  const filtersThatShouldBeVisible = Object.values(companyListPage.filters);
  for (const filter of filtersThatShouldBeVisible) {
    await expect(filter).toBeVisible();
  }
});

test('should switch to table view and display vendors', async() => {
  await companyListPage.goto();
  await companyListPage.tableViewButton.click();
  await expect(companyListPage.vendorCards.first()).toBeHidden();
  await expect(companyListPage.tableRows.first()).toBeVisible();
});

test('Should filter companies', async() => {
  await companyListPage.goto();
  await expect(companyListPage.vendorCards.first()).toBeVisible();

  const initialEngagedCount = await companyListPage.getConsultantsCount();

  await companyListPage.testTextFilter(initialEngagedCount, companyListPage.filters.search, 'Nordics');
  await companyListPage.testDropdownFilter(initialEngagedCount, companyListPage.filters.category);
  await companyListPage.testDropdownFilter(initialEngagedCount, companyListPage.filters.country);
  await companyListPage.testDropdownFilter(initialEngagedCount, companyListPage.filters.technologies);
  await companyListPage.testDropdownFilter(initialEngagedCount, companyListPage.filters.industry);
});

test.skip('should load more vendor cards', async() => {
  await companyListPage.goto();

  const initialCardCount = await companyListPage.vendorCards.count();
  expect(initialCardCount).toBeGreaterThan(0);
  expect(initialCardCount).toBeLessThan(3);

  expect(await companyListPage.loadMoreButton).toBeVisible();
  await companyListPage.loadMoreButton.click();

  const newCardCount = await companyListPage.vendorCards.count();
  expect(newCardCount).toBeGreaterThan(initialCardCount);
});

test.skip('should paginate through vendor table rows and verify row changes', async() => {
  await companyListPage.goto();
  await companyListPage.tableViewButton.click();

  const initialRows = await companyListPage.tableRows.allTextContents();
  expect(initialRows.length).toBeGreaterThan(0);
  expect(initialRows.length).toBeLessThan(3);

  await companyListPage.nioPagination.getByText('2').click();

  const newRows = await companyListPage.tableRows.allTextContents();
  expect(newRows.length).toBeGreaterThan(0);
  expect(newRows.length).toBeLessThan(3);
  expect(newRows).not.toEqual(initialRows);
});

test.skip('should filter vendor cards', async() => {
  await companyListPage.goto();
  const cardCount = await companyListPage.vendorCards.count();
  expect(cardCount).toBeGreaterThan(0);

  const textFilter = companyListPage.nioFilters.filter({ has: companyListPage.page.locator('input[type="text"]') }).first();

  await textFilter.getByRole('textbox').fill(SEARCH_TERM);
  const filteredCards = await companyListPage.vendorCards.allTextContents();
  expect(filteredCards[0]).toContain(SEARCH_TERM);
});

test.skip('should filter vendor table rows', async() => {
  await companyListPage.goto();

  await companyListPage.tableViewButton.click();
  const rowCount = await companyListPage.tableRows.count();
  expect(rowCount).toBeGreaterThan(0);

  const textFilter = companyListPage.nioFilters.filter({ has: companyListPage.page.locator('input[type="text"]') }).first();

  await textFilter.getByRole('textbox').fill(SEARCH_TERM);
  const filteredRows = await companyListPage.tableRows.allTextContents();
  expect(filteredRows[0]).toContain(SEARCH_TERM);
});

test.skip('should navigate to vendor detail page and close it', async({ page }) => {
  await companyListPage.goto();
  const cardCount = await companyListPage.vendorCards.count();
  expect(cardCount).toBeGreaterThan(0);

  await companyListPage.vendorCards.first().click();
  await expect(page).toHaveURL(/\/companies\/[^/]+$/);
  await expect(companyDetailPage.isDetailPageVisible()).toBeTruthy();

  await companyDetailPage.closeButton.click();
  await expect(page).toHaveURL('/companies');
});

// TEMP: Upload is disabled.
test.skip('should navigate to upload companies page', async({ page }) => {
  await companyListPage.goto();
  await companyListPage.uploadButton.click();
  await expect(page).toHaveURL('/companies/upload');
  await expect(page).toHaveTitle('Companies Upload | Nordics Platform');
});

test.skip('Import companies', async({ page }) => {
  await companyListPage.goto();
  await companyListPage.uploadButton.click();
  await expect(page.getByRole('heading', { name: 'Upload vendors' })).toBeVisible();

  const uploadPage = new CompanyUploadPage(page);
  await expect(uploadPage.uploadCard).toBeVisible();
  await expect(uploadPage.searchCompaniesCard).toBeVisible();

  // Import via CSV upload.
  await uploadPage.uploadCard.click();
  await uploadPage.uploadFile(sampleCompaniesCsvFilePath);
  await expect(page.getByText('Sit back and relax')).toBeVisible();

  const vendorCompanyCard = page.getByRole('listitem', { name: 'solarpath dynamics' });
  await expect(vendorCompanyCard).toBeVisible({ timeout: 15000 });
  await expect(vendorCompanyCard).toContainText(/^verified.*/, { timeout: 15000 });

  const unverifiedCard = page.getByRole('listitem', { name: 'bogisich' });
  await expect(unverifiedCard).toBeVisible();
  await expect(unverifiedCard).toContainText(/^unverified.*/, { timeout: 15000 });

  // TODO(Marian Rusnak): Figure out how to test alternatives. It is not working, because alternatives are not in the database.
  // const firstAvailableAlternative = unverifiedCard.getByRole('option').nth(0);
  // await expect(firstAvailableAlternative).toBeVisible();
  // const oopsCardName = await firstAvailableAlternative.textContent();
  // await firstAvailableAlternative.click();
  // const oopsCard = page.getByRole('listitem', { name: oopsCardName });
  // await expect(oopsCard.getByText('Pending')).toBeVisible();
  // await expect(oopsCard.getByText('Looks like we couldn\'t find this vendor.')).toBeVisible();

  await expect(unverifiedCard.getByRole('button', { name: 'Toggle popover' })).toBeVisible();
  await unverifiedCard.getByRole('button', { name: 'Toggle popover' }).click();
  await unverifiedCard.getByRole('button', { name: 'Delete vendor' }).click();
  await expect(unverifiedCard).toBeHidden();

  // Import via search.
  await uploadPage.searchCompaniesCard.click();
  await expect(uploadPage.searchCloseButton).toBeVisible();
  await expect(uploadPage.searchBox).toBeVisible();
  await uploadPage.searchBox.fill('Quant');
  await expect(uploadPage.searchResetButton).toBeVisible();
  const foundVendors = page.getByRole('option', { name: 'Found vendor' }).nth(0);
  await expect(foundVendors).toBeVisible();
  await foundVendors.nth(0).click();
  await expect(page.getByText('No result, try again')).toBeVisible();
  await uploadPage.searchResetButton.click();
  await expect(uploadPage.searchBox).toBeEmpty();
  await uploadPage.searchCloseButton.click();
  await expect(uploadPage.searchBox).toBeHidden();

  // Confirm import.
  expect(await uploadPage.allVerifiedCompanies.count()).toBe(2);
  await expect(uploadPage.addToListButton).toBeVisible();
  await uploadPage.addToListButton.click();
  await expect(page).toHaveTitle('Companies | Nordics Platform', { timeout: 15000 });

  const recentlyImportedCompanies = [
    'Solarpath Dynamics',
    'Quantum Nest'
  ];

  // Verify and cleanup recently imported companies.
  for (const companyName of recentlyImportedCompanies) {
    const companyCard = companyListPage.vendorCards.filter({ hasText: companyName });
    await expect(companyCard).toBeVisible();
    await companyCard.getByRole('button', { name: 'Delete' }).click();
    await expect(companyCard).toBeHidden();
  }
});
