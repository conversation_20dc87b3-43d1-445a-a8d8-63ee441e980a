<script setup lang="ts">
import { ref } from 'vue';
import type { TenderCandidate } from '@/modules/tenders/types/tenders-types';
import { CandidateStatus } from '@/modules/tenders/types/tenders-types';
import LabelBadge from '@/modules/tenders/components/LabelBadge.vue';
import NioScoreProgress from '@/common/components/NioScoreProgress.vue';
import { candidateScoreToPoints, candidateScoreToText, isScoreAvailable } from '@/common/utils/score';
import { getStatusLabel, getStatusClasses } from '@/common/utils/candidate-status';
import type { NioTableColumn } from '@/common/types/components';
import NioTableRow from '@/common/components/UI/table/NioTableRow.vue';
import NioTableIndexCol from '@/common/components/UI/table/NioTableIndexCol.vue';
import NioInfoBlock from '@/common/components/NioInfoBlock.vue';
import { defineAsyncComponent } from 'vue';
import NioRateVerdict from '@/common/components/NioRateVerdict.vue';
import CandidateStatusModal from '@/modules/tenders/components/tender-candidates/CandidateStatusModal.vue';
import PencilIcon from '@/assets/icons/pencil-icon.svg';

interface Props {
  candidate: TenderCandidate;
  index: number;
  allowInvite?: boolean;
  columns: NioTableColumn[];
  tenderId: string;
}

const props = withDefaults(defineProps<Props>(), { allowInvite: true });

const isPopoverOpen = ref(false);
const aboutMatch = ref(false);
const isStatusModalOpen = ref(false);
const candidateStatus = ref(props.candidate.application?.status || CandidateStatus.NEW);

const LazyMatchedCandidatePopover = defineAsyncComponent(() =>
  import('@/modules/tenders/components/tender-candidates/MatchedCandidatePopover.vue')
);

const openStatusModal = () => {
  isStatusModalOpen.value = true;
};

const handleStatusUpdated = (newStatus: CandidateStatus) => {
  candidateStatus.value = newStatus;
};

</script>

<template>
  <NioTableRow
    :key="candidate?.id"
    :columns="columns"
    expandable
  >
    <template #name>
      <NioTableIndexCol :index="index + 1">
        <div class="text-base">
          <span class="truncate line-clamp-1 break-all">
            <span class="hover:underline" @click.stop="aboutMatch = false; isPopoverOpen = true">{{ candidate.name }}</span>
          </span>
          <span class="truncate line-clamp-1 break-all text-sm font-light text-nio-grey-700">
            {{ candidate.company.name }}
          </span>
        </div>
      </NioTableIndexCol>
    </template>
    <template #position>
      <span class="truncate line-clamp-1 break-all text-base">
        {{ candidate.position.name }}
      </span>
    </template>
    <template #seniority>
      <span class="truncate line-clamp-1 break-all text-base capitalize">
        {{ candidate.seniority ?? '-' }}
      </span>
    </template>
    <template #rate>
      <div v-if="typeof candidate.rate === 'number'" class="text-base w-40 sm:w-50 md:w-60 lg:w-80">
        <NioRateVerdict :rate="candidate.rate" :low-value="candidate.position.price" :high-value="candidate.position.price_to" />
      </div>
      <span v-else class="truncate line-clamp-1 break-all text-base">
        -
      </span>
    </template>
    <template #status>
      <div class="flex items-center">
        <span
          class="inline-flex px-2 py-1 text-[12px] font-bold rounded-l-5 leading-[14px] uppercase whitespace-nowrap"
          :class="getStatusClasses(candidateStatus)"
          :aria-label="getStatusLabel(candidateStatus)"
          data-testid="candidate-status"
        >
          {{ getStatusLabel(candidateStatus) }}
        </span>
        <button
          class="inline-flex h-full justify-center items-center aspect-square rounded-r-5 bg-nio-blue-800 hover:bg-nio-blue-600-hover cursor-pointer"
          title="Edit status"
          @click.stop="openStatusModal"
        >
          <PencilIcon class="w-4 h-4 [&_path]:fill-white" />
        </button>
      </div>
    </template>
    <template #match>
      <div class="flex items-center space-x-sm justify-end">
        <NioScoreProgress
          v-if="isScoreAvailable(candidate.matching?.score)"
          :points="candidateScoreToPoints(candidate.matching?.score, 'total')"
          :title="candidateScoreToText(candidate.matching?.score)"
          size="sm"
        />
      </div>
    </template>
    <template #expand>
      <div class="flex items-center pl-3 mt-2 gap-3">
        <NioInfoBlock v-if="candidate.profession" title="Profession">
          <span class="capitalize">{{ candidate.profession }}</span>
        </NioInfoBlock>

        <NioInfoBlock v-if="candidate.country" title="Residence">
          <span class="capitalize">{{ candidate.country }}</span>
        </NioInfoBlock>

        <div v-if="candidate.matching?.technologies?.length">
          <div class="text-xs text-nio-grey-900/80 mb-1">
            <span>Technologies & Tools</span>
            <span v-if="candidate.matching?.technologies?.length > 3" class="underline ml-1 cursor-pointer text-xs text-nio-grey-900/80 hove" @click.stop="aboutMatch = true; isPopoverOpen = true">Show more</span>
          </div>
          <div class="font-bold">
            <div class="flex items-center gap-2">
              <div class="flex items-center gap-1">
                <LabelBadge
                  v-for="technology in candidate.matching?.technologies?.slice(0, 3)"
                  :key="technology.name"
                  :type="technology.score === 100 ? 'success' : technology.score > 0 && technology.score < 100 ? 'warning' : 'danger'"
                  size="sm"
                  :class="technology.score === 0 ? 'line-through' : ''"
                >
                  {{ technology.name }}
                </LabelBadge>
              </div>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-2 ml-auto">
          <button
            class=" px-3 py-1.5 border border-nio-blue-800 text-nio-blue-800 text-[12px] rounded-20 hover:bg-nio-blue-200 transition-colors cursor-pointer"
            @click.stop="aboutMatch = false; isPopoverOpen = true"
          >
            About Candidate
          </button>
          <button
            v-if="isScoreAvailable(candidate.matching?.score)"
            class="px-3 py-1.5 bg-nio-blue-800 text-nio-white text-[12px] border border-nio-blue-800 rounded-20 hover:bg-nio-blue-600-hover hover:border-nio-blue-600-hover transition-colors cursor-pointer"
            @click.stop="aboutMatch = true; isPopoverOpen = true"
          >
            About Match
          </button>
        </div>

        <component
          :is="LazyMatchedCandidatePopover"
          v-if="isPopoverOpen"
          v-model:open="isPopoverOpen"
          v-model:switched-to-about-match="aboutMatch"
          :candidate="candidate"
        />
      </div>
    </template>
  </NioTableRow>

  <CandidateStatusModal
    v-model="isStatusModalOpen"
    :tender-id="tenderId"
    :candidate-id="candidate.id"
    :candidate-name="candidate.name"
    :current-status="candidateStatus"
    @status-updated="handleStatusUpdated"
  />
</template>
