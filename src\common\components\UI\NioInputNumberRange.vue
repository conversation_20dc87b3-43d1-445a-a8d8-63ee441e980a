<template>
  <div class="relative">
    <div class="flex items-center gap-1 w-[180px]">
      <div class="flex-1 relative">
        <label
          :for="`${inputId}-min`"
          class="absolute -top-3.5 block text-xs font-medium text-nio-grey-700 mb-0"
        >
          Min
        </label>
        <input
          :id="`${inputId}-min`"
          v-model.number="minValue"
          type="number"
          :min="inputData?.component?.min"
          :max="inputData?.component?.max"
          :disabled="disabled"
          :placeholder="inputData?.component?.min?.toString() || '0'"
          :title="`Enter minimum hourly rate (${formatTooltip(inputData?.component?.min || 0)} - ${formatTooltip(inputData?.component?.max || 500)})`"
          class="w-full h-8 px-3 py-1 text-sm font-medium bg-nio-grey-background border border-gray-300 rounded-lg focus:outline-none focus:border-nio-blue-400 caret-nio-blue-800"
          :class="[
            minInputError ? 'border-nio-red-background' : 'border-gray-300',
            disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : 'text-nio-black-900'
          ]"
          :aria-describedby="`${inputData?.component?.payload_key}-min-errors${collectionIdentifier ?? ''}`"
          @input="validateAndUpdateMin"
        >
      </div>

      <div class="text-nio-grey-500">
        -
      </div>

      <div class="flex-1 relative">
        <label
          :for="`${inputId}-max`"
          class="absolute -top-3.5 right-0 block text-xs font-medium text-nio-grey-700 text-right mb-0"
        >
          Max
        </label>
        <input
          :id="`${inputId}-max`"
          v-model.number="maxValue"
          type="number"
          :min="inputData?.component?.min"
          :max="inputData?.component?.max"
          :disabled="disabled"
          :placeholder="inputData?.component?.max?.toString()"
          :title="`Enter maximum hourly rate (${formatTooltip(inputData?.component?.min || 0)} - ${formatTooltip(inputData?.component?.max || 500)})`"
          class="w-full h-8 px-3 py-1 text-sm font-medium bg-nio-grey-background border border-gray-300 rounded-lg focus:outline-none focus:border-nio-blue-400 caret-nio-blue-800"
          :class="[
            maxInputError ? 'border-nio-red-background' : 'border-gray-300',
            disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : 'text-nio-black-900'
          ]"
          :aria-describedby="`${inputData?.component?.payload_key}-max-errors${collectionIdentifier ?? ''}`"
          @input="validateAndUpdateMax"
        >
      </div>
    </div>

    <div
      v-if="error?.subKeys"
      :id="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
      class="absolute -bottom-4 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : ''"
    >
      {{ error.subKeys.min }}<span v-if="error.subKeys.max">, {{ error.subKeys.max }}</span>
    </div>
    <div
      v-else-if="minInputError || maxInputError"
      :id="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
      class="absolute -bottom-4 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : ''"
    >
      {{ [minInputError, maxInputError].filter(Boolean).join(', ') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DynamicFormItemData, FormErrors } from '@/common/utils/forms.ts';
import { debounce } from 'lodash-es';
import { watch, ref, computed } from 'vue';

type Props = {
  disabled?: boolean,
  tooltipPrepend?: string,
  tooltipAppend?: string,
  error?: FormErrors[0],
  errorPosition?: 'left' | 'right',
  inputData?: DynamicFormItemData,
  inputId?: string,
  collectionIdentifier?: number,
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  tooltipPrepend: '',
  tooltipAppend: '',
  error: undefined,
  errorPosition: 'left',
  inputData: undefined,
  inputId: undefined,
  collectionIdentifier: undefined,
});

const emit = defineEmits(['nio-blur']);
const model = defineModel<[number, number]>({ required: true });

if (model.value[0] === null) {
  model.value[0] = props.inputData?.component?.min ?? 0;
}
if (model.value[1] === null) {
  model.value[1] = props.inputData?.component?.max ?? 0;
}

// Reactive references for non-slider values
const minValue = ref<number>(model.value[0]);
const maxValue = ref<number>(model.value[1]);

// Local error states for non-slider inputs validation
const minInputError = ref<string>('');
const maxInputError = ref<string>('');

const minLimit = computed(() => props.inputData?.component?.min ?? 0);
const maxLimit = computed(() => props.inputData?.component?.max ?? Infinity);

const formatTooltip = (value: string | number) => `${props.tooltipPrepend}${value}${props.tooltipAppend}`;

const validateMinValue = (value: number): string => {
  if (isNaN(value) || value < minLimit.value || value > maxLimit.value) {
    return `Min rate must be between ${minLimit.value} and ${maxLimit.value}`;
  }

  if (maxValue.value === 0 || maxValue.value === null) {
    return '';
  }

  if (value > maxValue.value) {
    return `Min rate cannot be greater than max rate (${maxValue.value})`;
  }

  return '';
};

const validateMaxValue = (value: number): string => {
  if (isNaN(value) || value < minLimit.value || value > maxLimit.value) {
    return `Max rate must be between ${minLimit.value} and ${maxLimit.value}`;
  }

  if (value < minValue.value) {
    return `Max rate cannot be less than min rate (${minValue.value})`;
  }

  return '';
};

const validateAndUpdateMin = () => {
  minInputError.value = '';
  const error = validateMinValue(minValue.value);
  minInputError.value = error;

  if (!error) {
    if ((maxValue.value === 0 || maxValue.value === null) && minValue.value > 0) {
      maxValue.value = Math.max(minValue.value, minLimit.value);
      maxInputError.value = validateMaxValue(maxValue.value);
    }

    model.value = [minValue.value, maxValue.value];
  }
};

const validateAndUpdateMax = () => {
  maxInputError.value = '';
  const error = validateMaxValue(maxValue.value);
  maxInputError.value = error;

  if (!error) {
    model.value = [minValue.value, maxValue.value];
  }
};

watch(model, newValue => {
  minValue.value = newValue[0];
  maxValue.value = newValue[1];
  minInputError.value = '';
  maxInputError.value = '';
}, { deep: true });

const debouncedChangedInput = debounce(() => emit('nio-blur'), 500);
watch(model, () => {
  debouncedChangedInput();
});
</script>

<style lang="css">
.range-vars {
  --slider-bg: var(--color-nio-white);
  --slider-connect-bg: var(--color-nio-white);
  --slider-connect-bg-disabled: var(--color-nio-grey-background-60);
  --slider-height: 16px;
  --slider-vertical-height: 300px;
  --slider-radius: 9999px;

  --slider-handle-bg: var(--color-nio-blue-500);
  --slider-handle-border: 0;
  --slider-handle-width: 20px;
  --slider-handle-height: 16px;
  --slider-handle-radius: 9999px;
  --slider-handle-shadow: 0;
  --slider-handle-shadow-active: 0 0;
  --slider-handle-ring-width: 3px;
  --slider-handle-ring-color: #10B98130;

  --slider-tooltip-font-size: 0.875rem;
  --slider-tooltip-line-height: 1.25rem;
  --slider-tooltip-font-weight: 600;
  --slider-tooltip-min-width: 20px;
  --slider-tooltip-bg: var(--color-nio-blue-500);
  --slider-tooltip-bg-disabled: var(--color-nio-blue-500);
  --slider-tooltip-color: #fff;
  --slider-tooltip-radius: 5px;
  --slider-tooltip-py: 2px;
  --slider-tooltip-px: 6px;
  --slider-tooltip-arrow-size: 5px;
  --slider-tooltip-distance: 3px;

  .slider-handle {
    top: 0;
  }

  .slider-handle:hover {
    --slider-handle-bg: var(--color-nio-blue-800);
  }

  .slider-handle:active {
    --slider-handle-bg: var(--color-nio-blue-500);
    box-shadow: 0px 0px 0px 2px var(--nio-blue-outline-stroke-400, #BBD0FB);
  }
}
</style>
