<template>
  <PageContainerWrapper>
    <div class="grid lg:grid-cols-[4fr_1.5fr] gap-3">
      <PageCard>
        <TenderHeader v-if="tender" :tender="tender" @updated-tender="onUpdatedTender" />
        <TenderHeaderSkeleton v-else />
      </PageCard>

      <PageCard title="Invites" role="group" aria-label="Invites">
        <div v-if="!invitesSectionVisible" class="animate-pulse mt-[10px] flex flex-col h-full">
          <div class="grid grid-cols-2 gap-2 mb-4">
            <div>
              <div class="w-12 h-8 bg-nio-grey-100 rounded-10 mb-1" />
              <div class="w-8 h-4 bg-nio-grey-100 rounded-5" />
            </div>
            <div>
              <div class="w-12 h-8 bg-nio-grey-100 rounded-10 mb-1" />
              <div class="w-12 h-4 bg-nio-grey-100 rounded-5" />
            </div>
          </div>
          <div class="w-full h-10 bg-nio-grey-100 rounded-50 mt-auto" />
        </div>
        <template v-else-if="invitesSectionVisible">
          <div class="grid grid-cols-2 gap-2 mb-4">
            <div>
              <div class="font-bold text-h3 mt-1">
                {{ invitesSentCount }}
              </div>
              <div class="text-nio-grey-900">
                Sent
              </div>
            </div>
            <div>
              <div class="font-bold text-h3 mt-1">
                {{ invitesPendingCount }}
              </div>
              <div class="text-nio-grey-900">
                Pending
              </div>
            </div>
          </div>
          <button
            class="px-2 mt-auto py-2 w-full bg-nio-blue-800 flex items-center gap-1 justify-center text-nio-white text-[14px] rounded-50 hover:bg-nio-blue-600-hover transition-colors cursor-pointer"
            type="button"
            @click="showInvitePanel"
          >
            <span>Invite vendors & strategic partners</span>
          </button>
        </template>
        <div v-else-if="tender && tender.matching_in_progress && tenderStore.matchingStatus[CompaniesFilter.Selection] === MatchingStatus.InProgress" class="m-auto text-center">
          Available once matching is complete.
        </div>
      </PageCard>
    </div>

    <PageCard title="Positions" class="mt-3">
      <div
        v-if="tender?.positions?.length"
        class="flex gap-2 flex-wrap"
        style="grid-template-columns: repeat(auto-fit, 280px);"
      >
        <PositionsCard
          v-for="(position, i) in tender.positions"
          :key="i"
          :data="position"
        />
      </div>
      <div
        v-else
        class="flex gap-2 flex-wrap animate-pulse"
        style="grid-template-columns: repeat(auto-fit, 280px);"
      >
        <div v-for="i in 3" :key="i" class="w-[150px] h-[37px] bg-nio-grey-100 rounded-10 animate-pulse" />
      </div>
    </PageCard>

    <div class="grid lg:grid-cols-[1fr_1fr] gap-3 mt-3">
      <PageCard title="Best-Matched Vendors" subtitle="Vendors in your existing network, matched by past projects, tech stack, and team expertise.">
        <TenderMatchingWidget
          :allow-invite="true"
          :matching-type="CompaniesFilter.Selection"
          @polling-finished="onPollingFinished"
        />
      </PageCard>

      <PageCard
        title="Strategic Partners"
        subtitle="Large agencies or enterprise vendors in your Hub where expertise is harder to assess due to scale or service breadth."
        role="group"
        aria-label="Strategic partners group"
      >
        <div v-if="selectionMatchingInProgress" class="flex justify-center items-center h-full min-h-[200px]">
          <span class="text-[16px] text-center">Available once matching is complete.</span>
        </div>
        <OtherCompaniesTableWidget
          v-else
          :companies="otherVendors"
          :starting-index="1"
          :size="otherVendors.length"
          :tender-public-id="route.params.id as string"
          type="top"
          :loading="strategicPartnersLoading"
        />
      </PageCard>
    </div>
    <div
      v-if="!authStore.userProfile?.workspaces?.some(w => w.marketplace_disabled)"
      class="grid md:grid-cols-[3fr_1.25fr] lg:grid-cols-[3fr_1fr] bg-nio-grey-background overflow-hidden rounded-20 mt-3"
      role="group"
      aria-label="Marketplace container group"
    >
      <PageCard title="Marketplace" subtitle="New vendors outside your network with high match scores – ideal for testing and expanding your pool.">
        <TenderMatchingWidget
          :matching-type="CompaniesFilter.Marketplace"
          :allow-invite="false"
        />
      </PageCard>
      <div
        :style="{ backgroundImage: `url(${marketplaceBanner})` }"
        class="bg-cover bg-center min-h-[10.5rem] md:min-h-[350px] h-full w-full flex overflow-hidden flex-col"
      >
        <div class="bg-gradient-to-t p-5 mt-auto flex flex-col justify-start">
          <h6 class="text-nio-white mb-5 text-[28px] font-medium leading-[28px] tracking-[0.72px] capitalize ">
            Find pre-vetted <br> alternatives.
          </h6>
          <router-link :to="marketplaceRoute">
            <button
              class="px-4 py-2 border-2 border-white bg-white text-nio-blue-800 text-[14px] font-medium rounded-50 cursor-pointer flex items-center gap-1 justify-center transition-colors duration-300 hover:bg-transparent hover:text-white  "
            >
              <span>Explore</span><ArrowRight class="w-4 h-4" />
            </button>
          </router-link>
        </div>
      </div>
    </div>

    <PageCard v-if="invitedCompanies.length" title="Proposals" class="mt-3 min-h-[240px]">
      <div v-if="fetchingInvitedCompanies" class="w-full animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <div v-for="i in 3" :key="i" class="w-full h-[150px] bg-nio-grey-200 rounded-10" />
      </div>
      <TenderProposalsWidget v-else-if="invitedCompanies.length" :invited-companies="invitedCompanies" />
      <div class="flex justify-start mt-4">
        <router-link :to="{ name: 'tenderProposal', params: { id: $route.params.id } }">
          <button
            class="ps-4 pe-3 py-2 bg-nio-blue-800 text-nio-white text-[14px] rounded-50 hover:bg-nio-blue-600-hover transition-colors cursor-pointer flex items-center gap-1 justify-center"
          >
            <span>More</span>
            <ArrowRight class="w-4 h-4" />
          </button>
        </router-link>
      </div>
    </PageCard>

    <TenderInviteDrawer
      v-model:visible="inviteVisible"
      :expanded-sheet="expandedSheet"
      @update:expanded-sheet="expandedSheet = $event"
    />
  </PageContainerWrapper>
</template>

<script setup lang="ts">
import { onUnmounted, ref, computed, provide, onBeforeMount } from 'vue';
import { useRoute, onBeforeRouteUpdate } from 'vue-router';
import TenderHeader from '@/modules/tenders/components/TenderDetail/TenderHeader.vue';
import PositionsCard from '@/modules/tenders/components/TenderDetail/PositionsCard.vue';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store.ts';
import type { Company, Tender, CompanyWithProposalStats } from '@/modules/tenders/types/tenders-types.ts';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import { CompaniesFilter, MatchingStatus } from '@/modules/tenders/types/tenders-enums';
import TenderMatchingWidget from '../components/tender-matching/TenderMatchingWidget.vue';
import TenderInviteDrawer from '../components/TenderInviteDrawer.vue';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import OtherCompaniesTableWidget from '../components/OtherCompaniesTableWidget.vue';
import TenderProposalsWidget from '../components/TenderProposalsWidget.vue';
import PageCard from '@/common/components/PageCard.vue';
import { ArrowRight } from 'lucide-vue-next';
import marketplaceBanner from '@/assets/images/marketplace-bg.png';
import { routeMap } from '@/modules/tenders/routes';
import TenderHeaderSkeleton from '../components/TenderHeaderSkeleton.vue';
import { handleTenderError } from '@/modules/tenders/handlers/error';

const route = useRoute();
const tenderStore = useTenderStore();
const authStore = useAuthStore();

const tender = ref<Tender | undefined>();
const inviteVisible = ref(false);
const expandedSheet = ref(false);
const id = route.params.id as string;
const fetchingInvitedCompanies = ref(false);
const matchingCompaniesIds = ref(new Set<string>());
const otherVendors = computed(() => authStore?.userProfile?.workspaces
  ?.flatMap(workspace => workspace.companies)
  .filter(company => !matchingCompaniesIds.value.has(company.id)) || []);

const invitedCompanies = computed<CompanyWithProposalStats[]>(() => tenderStore.invitedVendors as CompanyWithProposalStats[]);
const invitesSentCount = computed(() => invitedCompanies.value.filter(c => c.sent_at).length);
const invitesPendingCount = computed(() => invitedCompanies.value.filter(c => !c.sent_at).length);
const invitesSectionVisible = computed(() => {
  const matchingStatus = tenderStore.matchingStatus[CompaniesFilter.Selection];
  // Show invites section when Matching is complete (any status)
  return matchingStatus;
});
const selectionMatchingInProgress = computed(() =>
  tenderStore.matchingStatus[CompaniesFilter.Selection] === MatchingStatus.InProgress
);
const strategicPartnersLoading = computed(() => {
  const matchingStatus = tenderStore.matchingStatus[CompaniesFilter.Selection];

  // Show loading when:
  // 1. Matching hasn't started yet (null) or is in progress
  // 2. AND no companies have been loaded from current matching session
  // 3. OR when invited companies are still being fetched (to avoid showing wrong invite statuses)
  const isMatchingActive = matchingStatus === null || matchingStatus === MatchingStatus.InProgress;
  const noCompaniesLoaded = matchingCompaniesIds.value.size < 1;
  const inviteStatusesLoading = fetchingInvitedCompanies.value;

  return (isMatchingActive && noCompaniesLoaded) || inviteStatusesLoading;
});

const marketplaceRoute = computed(() => ({ name: routeMap.detail.children.tenderMatchingMarketplace.name, params: { id: route.params.id } }));

provide('closeInviteSidebar', () => { inviteVisible.value = false; });
provide('openInviteSidebar', () => { inviteVisible.value = true; });

const showInvitePanel = () => {
  inviteVisible.value = true;
};

async function fetchTenderAndCompanies(id: string) {
  try {
    fetchingInvitedCompanies.value = true;
    matchingCompaniesIds.value.clear();
    const response = await tenderStore.fetchTenderDetail(id);
    tender.value = response;
    await tenderStore.fetchInvitedCompanies(id);
  } catch(error) {
    handleTenderError(error);
  } finally {
    fetchingInvitedCompanies.value = false;
  }
}

onBeforeMount(async() => {
  await fetchTenderAndCompanies(route.params.id as string);
});

onBeforeRouteUpdate(async(to, from, next) => {
  await fetchTenderAndCompanies(to.params.id as string);
  next();
});

const onPollingFinished = async(data: { topVendors: Company[]; otherVendors: Company[] }) => {
  for (const vendor of [...data.topVendors, ...data.otherVendors]) {
    if (vendor?.id) {
      matchingCompaniesIds.value.add(vendor.id);
    }
  }

  // Fetch invited companies if none are present
  if (!tenderStore.invitedVendors.length && !fetchingInvitedCompanies.value) {
    await fetchTenderAndCompanies(route.params.id as string);
  }
};

const onUpdatedTender = (updatedTender: Tender) => {
  tender.value = updatedTender;
};

onUnmounted(() => {
  tenderStore.stopMatchingPoll(id, CompaniesFilter.Selection);
});
</script>
