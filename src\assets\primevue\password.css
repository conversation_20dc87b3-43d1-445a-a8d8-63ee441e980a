@import './inputtext';

.p-password {
    @apply inline-flex relative
}

.p-password .p-password-overlay {
    @apply min-w-full
}

.p-password-meter {
    @apply h-3 bg-surface-200 dark:bg-surface-700 rounded-md
}

.p-password-meter-label {
    @apply h-full w-0 transition-[width] duration-1000 ease-in-out rounded-md
}

.p-password-meter-weak {
    @apply bg-red-500 dark:bg-red-400
}

.p-password-meter-medium {
    @apply bg-amber-500 dark:bg-amber-400
}

.p-password-meter-strong {
    @apply bg-green-500 dark:bg-green-400
}

.p-password-fluid {
    @apply flex
}

.p-password-fluid .p-password-input {
    @apply w-full
}

.p-password-input::-ms-reveal,
.p-password-input::-ms-clear {
    @apply hidden
}

.p-password-overlay {
    @apply p-3 rounded-md bg-surface-0 dark:bg-surface-900
        border border-surface-200 dark:border-surface-700
        text-surface-700 dark:text-surface-0
        shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]
}

.p-password-content {
    @apply flex flex-col gap-2
}

.p-password-toggle-mask-icon {
    @apply end-3 text-surface-500 dark:text-surface-400 absolute top-1/2 -mt-2 w-4 h-4
}

.p-password:has(.p-password-toggle-mask-icon) .p-password-input {
    @apply pe-10
}
