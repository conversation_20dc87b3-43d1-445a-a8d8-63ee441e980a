<script setup lang="ts">
import { computed } from 'vue';

import NioTableHeadRow from '@/common/components/UI/table/NioTableHeadRow.vue';
import type { NioTableColumn } from '@/common/types/components';

const props = defineProps<{
  columns: NioTableColumn[],
}>();

const columnGrid = computed(() => {
  const columnWidths = props.columns.map(col => col.width ?? 1).join('fr ') + 'fr';
  return `grid-template-columns: ${columnWidths}`;
});
</script>

<template>
  <div class="w-full overflow-x-auto overflow-y-hidden">
    <div class="grid" :style="columnGrid">
      <NioTableHeadRow :columns="columns" />
      <slot />
    </div>
  </div>
</template>
