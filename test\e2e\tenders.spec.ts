import { expect, test } from '@playwright/test';
import { TendersPage } from './page-objects/tenders/TendersPage';

const tender = {
  id: 'Yd0CGJ',
  name: '.NET Developer for Logistics Project',
};

test('Has tenders list', async({ page }) => {
  const tendersPage = new TendersPage(page);
  await tendersPage.goto();
  await expect(tendersPage.tenderItems.first()).toBeVisible();
  await tendersPage.tenderItems.first().getByRole('link', { name: 'Open' }).click();
  await expect(page).toHaveURL(/tenders\/[^/]+\/detail/);
});

test('Check rate pill display in tender card on list page', async({ page }) => {
  const tendersPage = new TendersPage(page);
  await tendersPage.goto();

  const tenderCard = tendersPage.getTenderCard(tender.name);
  await expect(tenderCard).toBeVisible();

  const ratePill = tendersPage.getTenderCardRatePill(tender.name);
  await expect(ratePill).toBeVisible();

  const ratePillText = await ratePill.textContent();
  expect(ratePillText).toMatch(/€\d+/);
});
