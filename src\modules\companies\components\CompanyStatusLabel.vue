<script setup lang="ts">
import { computed } from 'vue';
import { CompanyStatus } from '@/modules/auth/types/auth-types';

interface Props {
  status: CompanyStatus;
}

const props = defineProps<Props>();

const labelMap: Record<CompanyStatus, string> = {
  [CompanyStatus.DRAFT]: 'Unverified',
  [CompanyStatus.AWAITING_APPROVAL]: 'Awaiting',
  [CompanyStatus.PUBLISHED]: 'Verified',
};

const statusClassMap: Record<CompanyStatus, string> = {
  [CompanyStatus.DRAFT]: 'bg-nio-grey-100 text-nio-grey-700',
  [CompanyStatus.AWAITING_APPROVAL]: 'bg-orange-200 text-orange-600',
  [CompanyStatus.PUBLISHED]: 'bg-green-200 text-green-600',
};

const labelText = computed(() => labelMap[props.status]);
const statusClass = computed(() => statusClassMap[props.status]);
</script>

<template>
  <span :class="`${statusClass} rounded-full px-2 py-0.5 text-[12px] w-fit`">
    {{ labelText }}
  </span>
</template>
