<template>
  <PageContainerWrapper>
    <div class="grid lg:grid-cols-[4fr_1.5fr] gap-3">
      <PageCard>
        <TenderHeader
          v-if="tender"
          :tender="tender"
          :is-matching="true"
          @updated-tender="onUpdatedTender"
        />
        <TenderHeaderSkeleton v-else />
      </PageCard>

      <PageCard title="Invites">
        <div v-if="tender && !matchingLoadedBool" class="animate-pulse mt-[10px] flex flex-col h-full">
          <div class="grid grid-cols-2 gap-2 mb-4">
            <div>
              <div class="w-12 h-8 bg-nio-grey-100 rounded-10 mb-1" />
              <div class="w-8 h-4 bg-nio-grey-100 rounded-5" />
            </div>
            <div>
              <div class="w-12 h-8 bg-nio-grey-100 rounded-10 mb-1" />
              <div class="w-12 h-4 bg-nio-grey-100 rounded-5" />
            </div>
          </div>
          <div class="w-full h-10 bg-nio-grey-100 rounded-50 mt-auto" />
        </div>
        <template v-else-if="matchingLoadedBool">
          <div class="grid grid-cols-2 gap-2 mb-4">
            <div>
              <div class="font-bold text-h3 mt-1">
                {{ invitesSentCount }}
              </div>
              <div class="text-nio-grey-900">
                Sent
              </div>
            </div>
            <div>
              <div class="font-bold text-h3 mt-1">
                {{ invitesPendingCount }}
              </div>
              <div class="text-nio-grey-900">
                Pending
              </div>
            </div>
          </div>
          <button
            class="px-4 mt-auto py-2 w-full bg-nio-blue-800 flex items-center gap-1 justify-center text-nio-white text-[14px] rounded-50 hover:bg-nio-blue-600-hover transition-colors cursor-pointer"
            @click="showInvitePanel"
          >
            <span>Invite vendors & strategic partners</span>
          </button>
        </template>
        <div v-else-if="tender && tender.matching_in_progress" class="m-auto text-center">
          Available once matching is complete.
        </div>
      </PageCard>
    </div>

    <PageCard class="mt-3">
      <TenderMatching @matching-loaded="setMatchingLoaded" />
    </PageCard>

    <TenderInviteDrawer v-model:visible="inviteDrawerVisible" />
  </PageContainerWrapper>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import TenderHeader from '@/modules/tenders/components/TenderDetail/TenderHeader.vue';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import type { Tender } from '@/modules/tenders/types/tenders-types';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import TenderMatching from '../components/tender-matching/TenderMatching.vue';
import TenderInviteDrawer from '../components/TenderInviteDrawer.vue';
import PageCard from '@/common/components/PageCard.vue';
import TenderHeaderSkeleton from '../components/TenderHeaderSkeleton.vue';
import { handleTenderError } from '@/modules/tenders/handlers/error';

const route = useRoute();
const tenderStore = useTenderStore();
const tender = ref<Tender | undefined>();
const inviteDrawerVisible = ref(false);
const matchingLoaded = ref(false);
const matchingLoadedBool = computed(() => matchingLoaded.value);
const setMatchingLoaded = () => {
  matchingLoaded.value = true;
};

const showInvitePanel = () => {
  inviteDrawerVisible.value = true;
};

const invitedCompanies = computed(() => tenderStore.invitedVendors);
const invitesSentCount = computed(() => invitedCompanies.value.filter(c => c.sent_at).length);
const invitesPendingCount = computed(() => invitedCompanies.value.filter(c => !c.sent_at).length);

const onUpdatedTender = (updatedTender: Tender) => {
  tender.value = updatedTender;
};

onBeforeMount(async() => {
  const id = route.params.id as string;
  try{
    const response = await tenderStore.fetchTenderDetail(id);
    tender.value = response;
    await tenderStore.fetchInvitedCompanies(id);
  } catch (error) {
    handleTenderError(error);
  }
});
</script>
