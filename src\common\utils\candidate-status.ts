import { CandidateStatus } from '@/modules/tenders/types/tenders-types';

/**
 * Maps candidate status enum values to human-readable labels
 */
export const statusLabelMap: Record<CandidateStatus, string> = {
  [CandidateStatus.NEW]: 'New',
  [CandidateStatus.NOT_INTERESTED]: 'Not Interested',
  [CandidateStatus.INTERVIEW_SCHEDULED]: 'Interview Scheduled',
  [CandidateStatus.INTERVIEW_FAILED]: 'Interview Failed',
  [CandidateStatus.INTERVIEW_PASSED]: 'Interview Passed',
  [CandidateStatus.HIRED]: 'Hired',
  // Legacy statuses
  [CandidateStatus.REJECTED]: 'Rejected',
  [CandidateStatus.AWAITING_APPROVAL]: 'Awaiting Approval',
  [CandidateStatus.INTERESTED]: 'Interested'
};

/**
 * Maps candidate status enum values to CSS class combinations for styling
 */
export const statusClassMap: Record<CandidateStatus, string> = {
  [CandidateStatus.NEW]: 'bg-nio-grey-100 text-nio-grey-700',
  [CandidateStatus.NOT_INTERESTED]: 'bg-nio-red-background text-nio-red-500',
  [CandidateStatus.INTERVIEW_SCHEDULED]: 'bg-nio-blue-200 text-nio-blue-800',
  [CandidateStatus.INTERVIEW_FAILED]: 'bg-nio-red-background text-nio-red-500',
  [CandidateStatus.INTERVIEW_PASSED]: 'bg-nio-green-bg text-nio-green-text',
  [CandidateStatus.HIRED]: 'bg-nio-green-bg text-nio-green-text',
  // Legacy statuses
  [CandidateStatus.REJECTED]: 'bg-nio-red-background text-nio-red-500',
  [CandidateStatus.AWAITING_APPROVAL]: 'bg-nio-grey-100 text-nio-grey-700',
  [CandidateStatus.INTERESTED]: 'bg-nio-blue-200 text-nio-blue-800'
};

/**
 * Status options for dropdowns and forms, excluding legacy statuses
 */
export const statusOptions = [
  { value: CandidateStatus.NEW, label: statusLabelMap[CandidateStatus.NEW] },
  { value: CandidateStatus.INTERVIEW_SCHEDULED, label: statusLabelMap[CandidateStatus.INTERVIEW_SCHEDULED] },
  { value: CandidateStatus.NOT_INTERESTED, label: statusLabelMap[CandidateStatus.NOT_INTERESTED] },
  { value: CandidateStatus.INTERVIEW_PASSED, label: statusLabelMap[CandidateStatus.INTERVIEW_PASSED] },
  { value: CandidateStatus.INTERVIEW_FAILED, label: statusLabelMap[CandidateStatus.INTERVIEW_FAILED] },
  { value: CandidateStatus.HIRED, label: statusLabelMap[CandidateStatus.HIRED] }
];

/**
 * Get human-readable label for a candidate status
 * @param status - The candidate status enum value
 * @returns The human-readable label
 */
export const getStatusLabel = (status: CandidateStatus): string => statusLabelMap[status];

/**
 * Get CSS classes for styling a candidate status
 * @param status - The candidate status enum value
 * @returns The CSS class string for styling
 */
export const getStatusClasses = (status: CandidateStatus): string => statusClassMap[status];

/**
 * Check if a status is a legacy status that should be handled differently
 * @param status - The candidate status enum value
 * @returns True if the status is a legacy status
 */
export const isLegacyStatus = (status: CandidateStatus): boolean => {
  return [
    CandidateStatus.REJECTED,
    CandidateStatus.AWAITING_APPROVAL,
    CandidateStatus.INTERESTED
  ].includes(status);
};
