<template>
  <div ref="textDiv">
    {{ text }}
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { transformText } from '@/common/utils/text';

interface Props {
  text: string,
  speedMultiplier?: number,
}

const props = withDefaults(defineProps<Props>(), {
  speedMultiplier: 1
});

const textDiv = ref<HTMLDivElement>();

onMounted(() => {
  transformText(textDiv.value!, props.speedMultiplier);
});

watch(() => props.text, () => {
  transformText(textDiv.value!, props.speedMultiplier);
});
</script>
