.DS_Store
node_modules
/dist
storybook-static


# local env files
*.env
.env.backup
.env.*.backup
.env.test
.env.local
.env.*.local
.env.production

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
.vscode
!.vscode/settings.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*storybook.log

# Sentry Config File
.env.sentry-build-plugin

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# e2e arifacts
/test/e2e/.auth/
/api/

# Security
security-reports/retire-report.json

/.cursor
CLAUDE.md
