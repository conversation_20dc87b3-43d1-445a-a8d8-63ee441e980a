<script setup lang="ts">
import ConfirmationModal from '@/common/components/popover/ConfirmationModal.vue';
import { ref } from 'vue';

const modal = ref<InstanceType<typeof ConfirmationModal>>();

const showModal = () => {
  modal.value?.show();
};

</script>

<template>
  <div>
    <button
      class="bg-red-500"
      @click="showModal"
    >
      Open modal
    </button>
    <ConfirmationModal
      ref="modal"
      modal-title="Test title"
      modal-description="test desc"
      confirm-button-text="Confirm"
    />
  </div>
</template>
