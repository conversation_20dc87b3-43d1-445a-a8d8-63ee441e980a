import { type Locator, type Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class TenderPage extends BasePage {
  readonly header: Locator;
  readonly submissionDeadline: Locator;
  readonly submissionDeadlineEditButton: Locator;
  readonly invitesContainer: Locator;
  readonly strategicPartnersContainer: Locator;
  readonly marketplaceContainer: Locator;
  readonly candidatesTable: Locator;
  readonly candidateStatus: Locator;
  readonly editStatusButton: Locator;
  readonly statusModal: Locator;
  readonly statusDropdown: Locator;
  readonly statusModalSubmitButton: Locator;
  readonly ratePill: Locator;

  constructor(page: Page) {
    super(page);
    this.header = page.getByRole('group', { name: 'Tender header' });
    this.submissionDeadline = this.header.getByLabel('Submission deadline');
    this.submissionDeadlineEditButton = this.header.getByRole('button', { name: 'Edit submission deadline' });
    this.invitesContainer = this.page.getByRole('group', { name: 'Invites' });
    this.strategicPartnersContainer = this.page.getByRole('group', { name: 'Strategic partners' });
    this.marketplaceContainer = this.page.getByRole('group', { name: 'Marketplace container' });
    this.candidatesTable = page.getByTestId('candidates-table');
    this.candidateStatus = page.getByTestId('candidate-status');
    this.editStatusButton = page.getByRole('button', { name: 'Edit status' });
    this.statusModal = page.getByRole('dialog', { name: 'Update Status' });
    this.statusDropdown = this.statusModal.getByLabel('Status');
    this.statusModalSubmitButton = page.getByRole('button', { name: 'Submit' });
    this.ratePill = this.header.getByText(/€\d+/);
  }
}
