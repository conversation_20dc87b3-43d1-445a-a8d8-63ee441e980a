<template>
  <PageContainerWrapper>
    <PageCard>
      <TenderHeader
        v-if="tender"
        :tender="tender"
        :is-matching="true"
        @updated-tender="onUpdatedTender"
      />
      <TenderHeaderSkeleton v-else />
    </PageCard>

    <PageCard class="mt-3">
      <TenderMarketplaceMatching />
    </PageCard>
  </PageContainerWrapper>
</template>

<script setup lang="ts">
import { onBeforeMount, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import TenderHeader from '@/modules/tenders/components/TenderDetail/TenderHeader.vue';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import type { Tender } from '@/modules/tenders/types/tenders-types';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import TenderMarketplaceMatching from '@/modules/tenders/components/tender-matching/TenderMarketplaceMatching.vue';
import PageCard from '@/common/components/PageCard.vue';
import TenderHeaderSkeleton from '../components/TenderHeaderSkeleton.vue';
import { handleTenderError } from '@/modules/tenders/handlers/error';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import { routeMap } from '@/modules/tenders/routes';

const route = useRoute();
const router = useRouter();
const tenderStore = useTenderStore();
const authStore = useAuthStore();
const tender = ref<Tender | undefined>();

const onUpdatedTender = (updatedTender: Tender) => {
  tender.value = updatedTender;
};

onBeforeMount(async() => {
  const id = route.params.id as string;
  if (authStore.userProfile?.workspaces.at(0)?.marketplace_disabled) {
    await router.replace({ name: routeMap.detail.children.tenderDetail.name, params: { id } });
    return;
  }
  try{
    const response = await tenderStore.fetchTenderDetail(id);
    tender.value = response;
  } catch (error) {
    handleTenderError(error);
  }
});
</script>
