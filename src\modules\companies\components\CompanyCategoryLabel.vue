<script setup lang="ts">
import { computed } from 'vue';
import { CompanyCategory } from '@/modules/auth/types/auth-types';
import { Building, House, Building2 } from 'lucide-vue-next';

interface Props {
  category: CompanyCategory;
}

const props = defineProps<Props>();

const categoryTextMap: Record<CompanyCategory, string> = {
  [CompanyCategory.ENTERPRISE]: 'Enterprise',
  [CompanyCategory.AGENCY]: 'Agency',
  [CompanyCategory.SOFTWARE_HOUSE]: 'Software House',
};

const categoryText = computed(() => {
  return categoryTextMap[props.category];
});
</script>

<template>
  <span class="w-fit px-1 py-0.5 border border-nio-grey-100 text-xs font-normal text-nio-grey-700 rounded-5 bg-nio-grey-100 inline-flex items-center gap-1 leading-[18px]">
    <Building v-if="category === CompanyCategory.AGENCY" stroke-width="2" class="w-3 h-3" />
    <House v-else-if="category === CompanyCategory.SOFTWARE_HOUSE" stroke-width="2" class="w-3 h-3 " />
    <Building2 v-else-if="category === CompanyCategory.ENTERPRISE" stroke-width="2" class="w-3 h-3 " />
    <span> {{ categoryText }}</span>
  </span>
</template>
