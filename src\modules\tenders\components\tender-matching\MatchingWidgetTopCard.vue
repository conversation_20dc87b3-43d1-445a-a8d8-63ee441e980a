<script setup lang="ts">
import type { Company } from '@/modules/tenders/types/tenders-types';
import { computed } from 'vue';
import { useTenderStore } from '../../stores/tenders-store';
import { tenderScoreToPoints, tenderScoreToText } from '@/common/utils/score';
import NioScoreProgress from '@/common/components/NioScoreProgress.vue';
import { useScreenWidth } from '@/common/composables/useScreenWidth';
import InviteChip from '@/modules/tenders/components/InviteChip.vue';

interface Props {
  company: Company;
  tenderId: string;
  invited?: boolean;
  allowInvite?: boolean;
  index: number;
}

const props = withDefaults(defineProps<Props>(), {
  allowInvite: true,
});

const { screenWidth } = useScreenWidth();

const tenderStore = useTenderStore();
const invitedCompanyIds = computed(() => tenderStore.invitedVendors.filter(vendor => Boolean(vendor.sent_at)).map(vendor => vendor.id));
const pendingCompanyIds = computed(() => tenderStore.invitedVendors.filter(vendor => !vendor.sent_at).map(vendor => vendor.id));
const isVendorInvited = computed(() => invitedCompanyIds.value.includes(props.company.id));
const isVendorPending = computed(() => pendingCompanyIds.value.includes(props.company.id));

const toggleInvite = () => {
  if (!props.allowInvite || isVendorInvited.value) {
    return;
  }
  if (isVendorPending.value) {
    tenderStore.uninviteCompany(props.tenderId, props.company.id);
  } else {
    tenderStore.inviteCompany(props.tenderId, props.company.id);
  }
};
</script>

<template>
  <div v-if="props.company" class="group grid grid-cols-[1fr_4.5rem] xl:grid-cols-[1fr_7.5rem] min-h-[5rem] w-full">
    <div class="flex flex-col justify-center items-start bg-[#F2F2F7] rounded-l-20 h-full w-full p-3">
      <div class="flex">
        <span class="text-[12px] font-paragraph text-nio-grey-300 leading-normal mr-2 pt-1">
          {{ index + 1 }}
        </span>
        <div>
          <template v-if="allowInvite">
            <InviteChip :status="isVendorInvited ? 'invited' : (isVendorPending ? 'pending' : 'not-invited')" />
          </template>
          <h3 class="text-h5 font-paragraph text-black leading-[24px] line-clamp-2 break-all">
            {{ props.company.name }}
          </h3>
          <p class="font-paragraph text-sm leading-normal text-nio-grey-500">
            {{ props.company.headquarters }}, {{ props.company.country }}
          </p>
        </div>
      </div>
    </div>

    <div class="flex flex-col justify-center items-center bg-[#E5E5EA] rounded-r-20 h-full text-center">
      <NioScoreProgress
        :size="screenWidth > 1280 ? 'md' : 'sm'"
        :class="allowInvite && !isVendorInvited ? 'group-hover:hidden' : ''"
        :points="tenderScoreToPoints(props.company.match_details?.overall_score, 'total')"
        :title="tenderScoreToText(props.company.match_details?.overall_score)"
      />

      <button
        v-if="allowInvite && !isVendorInvited"
        class="hidden group-hover:block px-4 py-2 text-white text-[14px] rounded-50 transition-colors cursor-pointer"
        :class="isVendorPending ? 'bg-nio-red-500 hover:bg-nio-red-500/80' : 'bg-nio-green-text hover:bg-nio-green-text/80'"
        @click="toggleInvite"
      >
        {{ isVendorPending ? 'Cancel invite' : 'Invite' }}
      </button>
    </div>
  </div>
</template>
