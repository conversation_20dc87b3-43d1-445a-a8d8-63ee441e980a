import { expect, test } from '@playwright/test';
import { ConsultantsPage } from './page-objects/consultants/ConsultantsPage';
import { DateTime } from 'luxon';

test('Has consultants engaged/available list and filters', async({ page }) => {
  const consultantsPage = new ConsultantsPage(page);
  await consultantsPage.gotoEngaged();
  await expect(consultantsPage.consultants.first()).toBeVisible({ timeout: 20000 });
  let filtersThatShouldBeVisible = [
    ...Object.values(consultantsPage.sharedFilters),
    ...Object.values(consultantsPage.engagedFilters)
  ];
  for (const filter of filtersThatShouldBeVisible) {
    await expect(filter).toBeVisible();
  }
  await consultantsPage.gotoAvailable();
  await expect(consultantsPage.consultants.first()).toBeVisible({ timeout: 20000 });
  filtersThatShouldBeVisible = [
    ...Object.values(consultantsPage.sharedFilters),
    ...Object.values(consultantsPage.availableFilters)
  ];
  for (const filter of filtersThatShouldBeVisible) {
    await expect(filter).toBeVisible();
  }
});

test('Should filter consultants on both engaged and available pages', async({ page }) => {
  test.setTimeout(60000);
  const consultantsPage = new ConsultantsPage(page);

  await consultantsPage.gotoEngaged();
  await expect(consultantsPage.consultants.first()).toBeVisible({ timeout: 20000 });

  const initialEngagedCount = await consultantsPage.getConsultantsCount();
  const futureDateString = DateTime.now().plus({ months: 1 }).toISODate();

  await consultantsPage.testTextFilter(initialEngagedCount, consultantsPage.sharedFilters.search, 'John');
  await consultantsPage.testTextFilter(initialEngagedCount, consultantsPage.sharedFilters.profession, 'Data');

  await consultantsPage.testDropdownFilter(initialEngagedCount, consultantsPage.sharedFilters.seniority);
  await consultantsPage.testDropdownFilter(initialEngagedCount, consultantsPage.sharedFilters.company);
  await consultantsPage.testDropdownFilter(initialEngagedCount, consultantsPage.sharedFilters.technologies);
  await consultantsPage.testDropdownFilter(initialEngagedCount, consultantsPage.engagedFilters.managedBy);
  await consultantsPage.testDropdownFilter(initialEngagedCount, consultantsPage.engagedFilters.tender);
  await consultantsPage.testDropdownFilter(initialEngagedCount, consultantsPage.sharedFilters.residence);

  await consultantsPage.testNumberFilter(initialEngagedCount, consultantsPage.sharedFilters.minRate, '100');
  await consultantsPage.testNumberFilter(initialEngagedCount, consultantsPage.sharedFilters.maxRate, '500');

  await consultantsPage.testDateFilter(initialEngagedCount, consultantsPage.engagedFilters.engagedFrom, futureDateString);
  await consultantsPage.testDateFilter(initialEngagedCount, consultantsPage.engagedFilters.engagedTo, futureDateString);

  // Test filters on Available page
  await consultantsPage.gotoAvailable();
  await expect(consultantsPage.consultants.first()).toBeVisible({ timeout: 20000 });

  const initialAvailableCount = await consultantsPage.getConsultantsCount();

  await consultantsPage.testTextFilter(initialAvailableCount, consultantsPage.sharedFilters.search, 'John');
  await consultantsPage.testTextFilter(initialAvailableCount, consultantsPage.sharedFilters.profession, 'Data');

  await consultantsPage.testDropdownFilter(initialAvailableCount, consultantsPage.sharedFilters.seniority);
  await consultantsPage.testDropdownFilter(initialAvailableCount, consultantsPage.sharedFilters.company);
  await consultantsPage.testDropdownFilter(initialAvailableCount, consultantsPage.sharedFilters.technologies);
  await consultantsPage.testDropdownFilter(initialAvailableCount, consultantsPage.sharedFilters.residence);

  await consultantsPage.testNumberFilter(initialAvailableCount, consultantsPage.sharedFilters.minRate, '100');
  await consultantsPage.testNumberFilter(initialAvailableCount, consultantsPage.sharedFilters.maxRate, '500');

  await consultantsPage.testDateFilter(initialAvailableCount, consultantsPage.availableFilters.availableFrom, futureDateString);
  await consultantsPage.testDateFilter(initialAvailableCount, consultantsPage.availableFilters.availableTo, futureDateString);
});
